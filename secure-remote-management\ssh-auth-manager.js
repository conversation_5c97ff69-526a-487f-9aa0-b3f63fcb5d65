#!/usr/bin/env node

/**
 * 🔐 SSH Authentication Manager
 * Secure SSH key-based authentication system for remote server management
 * 
 * Features:
 * - Automated SSH key generation and deployment
 * - Secure key management with rotation
 * - Connection verification and health checks
 * - Multi-server support with centralized key management
 */

const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const { execSync, spawn } = require('child_process');
const crypto = require('crypto');

class SSHAuthManager {
  constructor(config = {}) {
    this.config = {
      serverIP: config.serverIP || '***********',
      serverUser: config.serverUser || 'root',
      keySize: config.keySize || 4096,
      keyType: config.keyType || 'rsa',
      keyName: config.keyName || 'streamdb-remote-management',
      sshDir: config.sshDir || path.join(os.homedir(), '.ssh'),
      connectionTimeout: config.connectionTimeout || 10,
      maxRetries: config.maxRetries || 3,
      ...config
    };

    this.keyPath = path.join(this.config.sshDir, this.config.keyName);
    this.publicKeyPath = `${this.keyPath}.pub`;
    this.configPath = path.join(this.config.sshDir, 'config');
    this.knownHostsPath = path.join(this.config.sshDir, 'known_hosts');
  }

  /**
   * Initialize SSH authentication system
   */
  async initialize() {
    try {
      console.log('🔐 Initializing SSH Authentication Manager...');
      
      await this.ensureSSHDirectory();
      await this.generateOrVerifyKeys();
      await this.updateSSHConfig();
      await this.verifyConnection();
      
      console.log('✅ SSH Authentication Manager initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize SSH Authentication Manager:', error.message);
      throw error;
    }
  }

  /**
   * Ensure SSH directory exists with proper permissions
   */
  async ensureSSHDirectory() {
    try {
      await fs.access(this.config.sshDir);
    } catch {
      console.log('📁 Creating SSH directory...');
      await fs.mkdir(this.config.sshDir, { mode: 0o700, recursive: true });
    }

    // Ensure proper permissions
    await fs.chmod(this.config.sshDir, 0o700);
    console.log('✅ SSH directory ready');
  }

  /**
   * Generate new SSH key pair or verify existing keys
   */
  async generateOrVerifyKeys() {
    const keyExists = await this.checkKeyExists();
    
    if (keyExists) {
      console.log('🔑 Existing SSH key found, verifying...');
      const isValid = await this.verifyKeyIntegrity();
      
      if (isValid) {
        console.log('✅ Existing SSH key is valid');
        return;
      } else {
        console.log('⚠️  Existing SSH key is invalid, generating new key...');
      }
    } else {
      console.log('🔑 Generating new SSH key pair...');
    }

    await this.generateKeyPair();
  }

  /**
   * Check if SSH key pair exists
   */
  async checkKeyExists() {
    try {
      await fs.access(this.keyPath);
      await fs.access(this.publicKeyPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify SSH key integrity
   */
  async verifyKeyIntegrity() {
    try {
      // Check private key
      const privateKey = await fs.readFile(this.keyPath, 'utf8');
      if (!privateKey.includes('BEGIN') || !privateKey.includes('PRIVATE KEY')) {
        return false;
      }

      // Check public key
      const publicKey = await fs.readFile(this.publicKeyPath, 'utf8');
      if (!publicKey.includes('ssh-rsa') && !publicKey.includes('ssh-ed25519')) {
        return false;
      }

      // Verify key pair match
      const fingerprint = this.execCommand(`ssh-keygen -l -f "${this.publicKeyPath}"`);
      return fingerprint.includes('SHA256');
    } catch {
      return false;
    }
  }

  /**
   * Generate new SSH key pair
   */
  async generateKeyPair() {
    const comment = `${this.config.keyName}-${Date.now()}`;
    
    const command = [
      'ssh-keygen',
      '-t', this.config.keyType,
      '-b', this.config.keySize.toString(),
      '-f', this.keyPath,
      '-N', '""',
      '-C', comment
    ].join(' ');

    try {
      this.execCommand(command);
      
      // Set proper permissions
      await fs.chmod(this.keyPath, 0o600);
      await fs.chmod(this.publicKeyPath, 0o644);
      
      console.log('✅ SSH key pair generated successfully');
      
      // Display key fingerprint
      const fingerprint = this.execCommand(`ssh-keygen -l -f "${this.publicKeyPath}"`);
      console.log(`🔍 Key fingerprint: ${fingerprint.trim()}`);
      
    } catch (error) {
      throw new Error(`Failed to generate SSH key pair: ${error.message}`);
    }
  }

  /**
   * Update SSH config for easier connection management
   */
  async updateSSHConfig() {
    const hostAlias = 'streamdb-backend';
    const configEntry = `
# StreamDB Remote Management Server
Host ${hostAlias}
    HostName ${this.config.serverIP}
    User ${this.config.serverUser}
    IdentityFile ${this.keyPath}
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout ${this.config.connectionTimeout}
    StrictHostKeyChecking no
    UserKnownHostsFile ${this.knownHostsPath}
`;

    try {
      let configContent = '';
      try {
        configContent = await fs.readFile(this.configPath, 'utf8');
      } catch {
        // Config file doesn't exist, will create new one
      }

      // Remove existing entry if present
      const lines = configContent.split('\n');
      const filteredLines = [];
      let skipSection = false;

      for (const line of lines) {
        if (line.trim().startsWith(`Host ${hostAlias}`)) {
          skipSection = true;
          continue;
        }
        if (skipSection && line.trim().startsWith('Host ') && !line.includes(hostAlias)) {
          skipSection = false;
        }
        if (!skipSection) {
          filteredLines.push(line);
        }
      }

      // Add new entry
      const newConfig = filteredLines.join('\n').trim() + configEntry;
      
      await fs.writeFile(this.configPath, newConfig);
      await fs.chmod(this.configPath, 0o600);
      
      console.log('✅ SSH config updated');
    } catch (error) {
      console.warn('⚠️  Failed to update SSH config:', error.message);
    }
  }

  /**
   * Deploy public key to remote server
   */
  async deployPublicKey() {
    try {
      console.log('🚀 Deploying public key to remote server...');
      
      const publicKey = await fs.readFile(this.publicKeyPath, 'utf8');
      
      // Use ssh-copy-id if available, otherwise manual deployment
      try {
        const command = `ssh-copy-id -i "${this.publicKeyPath}" ${this.config.serverUser}@${this.config.serverIP}`;
        this.execCommand(command);
        console.log('✅ Public key deployed using ssh-copy-id');
      } catch {
        // Fallback to manual deployment
        await this.manualKeyDeployment(publicKey.trim());
      }
      
      return true;
    } catch (error) {
      throw new Error(`Failed to deploy public key: ${error.message}`);
    }
  }

  /**
   * Manual public key deployment
   */
  async manualKeyDeployment(publicKey) {
    console.log('📝 Attempting manual key deployment...');
    
    const commands = [
      'mkdir -p ~/.ssh',
      'chmod 700 ~/.ssh',
      `echo "${publicKey}" >> ~/.ssh/authorized_keys`,
      'chmod 600 ~/.ssh/authorized_keys',
      'sort ~/.ssh/authorized_keys | uniq > ~/.ssh/authorized_keys.tmp',
      'mv ~/.ssh/authorized_keys.tmp ~/.ssh/authorized_keys'
    ];

    for (const cmd of commands) {
      const sshCommand = `ssh ${this.config.serverUser}@${this.config.serverIP} "${cmd}"`;
      this.execCommand(sshCommand);
    }
    
    console.log('✅ Public key deployed manually');
  }

  /**
   * Verify SSH connection
   */
  async verifyConnection() {
    console.log('🔍 Verifying SSH connection...');
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        const result = this.execCommand(
          `ssh -o ConnectTimeout=${this.config.connectionTimeout} -o BatchMode=yes ${this.config.serverUser}@${this.config.serverIP} "echo 'Connection successful'"`,
          { timeout: (this.config.connectionTimeout + 5) * 1000 }
        );
        
        if (result.includes('Connection successful')) {
          console.log('✅ SSH connection verified successfully');
          return true;
        }
      } catch (error) {
        console.log(`❌ Connection attempt ${attempt}/${this.config.maxRetries} failed: ${error.message}`);
        
        if (attempt === this.config.maxRetries) {
          throw new Error('SSH connection verification failed after all retries');
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    return false;
  }

  /**
   * Execute shell command synchronously
   */
  execCommand(command, options = {}) {
    try {
      const result = execSync(command, {
        encoding: 'utf8',
        timeout: options.timeout || 30000,
        ...options
      });
      return result.toString().trim();
    } catch (error) {
      throw new Error(`Command failed: ${command}\nError: ${error.message}`);
    }
  }

  /**
   * Get connection status
   */
  async getConnectionStatus() {
    try {
      await this.verifyConnection();
      return {
        connected: true,
        server: this.config.serverIP,
        user: this.config.serverUser,
        keyPath: this.keyPath,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        connected: false,
        server: this.config.serverIP,
        user: this.config.serverUser,
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Rotate SSH keys (generate new key pair)
   */
  async rotateKeys() {
    console.log('🔄 Rotating SSH keys...');
    
    // Backup current keys
    const timestamp = Date.now();
    const backupDir = path.join(this.config.sshDir, 'backups');
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
      await fs.copyFile(this.keyPath, path.join(backupDir, `${this.config.keyName}.${timestamp}`));
      await fs.copyFile(this.publicKeyPath, path.join(backupDir, `${this.config.keyName}.${timestamp}.pub`));
    } catch (error) {
      console.warn('⚠️  Failed to backup old keys:', error.message);
    }
    
    // Generate new keys
    await this.generateKeyPair();
    await this.deployPublicKey();
    await this.verifyConnection();
    
    console.log('✅ SSH keys rotated successfully');
  }
}

module.exports = SSHAuthManager;

// CLI usage
if (require.main === module) {
  const manager = new SSHAuthManager();
  
  manager.initialize()
    .then(() => {
      console.log('🎉 SSH Authentication Manager ready for use');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Initialization failed:', error.message);
      process.exit(1);
    });
}
