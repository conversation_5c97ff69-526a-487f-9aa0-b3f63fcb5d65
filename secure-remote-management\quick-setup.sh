#!/bin/bash

# 🚀 Quick Setup for AI Direct Server Access
# One command to rule them all!

set -e

echo "🔒 StreamDB AI Direct Access Setup"
echo "=================================="
echo ""
echo "This will set up secure direct access for AI troubleshooting."
echo "After this, I can directly fix your server issues without back-and-forth!"
echo ""

# Check if we're on Windows (Git Bash/WSL)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    echo "⚠️  Detected Windows environment"
    echo "Please ensure you have:"
    echo "  - Git Bash or WSL installed"
    echo "  - Node.js 16+ installed"
    echo "  - SSH client available"
    echo ""
fi

# Quick dependency check
if ! command -v node >/dev/null 2>&1; then
    echo "❌ Node.js not found. Please install Node.js 16+ first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

if ! command -v ssh >/dev/null 2>&1; then
    echo "❌ SSH not found. Please install OpenSSH client first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Create management directory
echo "📁 Creating management directory..."
mkdir -p ~/.streamdb-ai-access
cd ~/.streamdb-ai-access

# Create package.json
echo "📦 Setting up Node.js environment..."
cat > package.json << 'EOF'
{
  "name": "streamdb-ai-access",
  "version": "1.0.0",
  "dependencies": {
    "node-ssh": "^13.1.0",
    "express": "^4.18.2",
    "cors": "^2.8.5"
  }
}
EOF

# Install dependencies
npm install --silent

# Generate SSH key
echo "🔑 Setting up SSH key..."
mkdir -p ~/.ssh
chmod 700 ~/.ssh

if [ ! -f ~/.ssh/streamdb_ai ]; then
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/streamdb_ai -N "" -C "streamdb-ai-access" >/dev/null 2>&1
    chmod 600 ~/.ssh/streamdb_ai
    chmod 644 ~/.ssh/streamdb_ai.pub
    echo "✅ SSH key generated"
else
    echo "✅ SSH key already exists"
fi

# Create the management server
echo "🖥️  Creating management server..."
cat > server.js << 'EOF'
const { NodeSSH } = require('node-ssh');
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const os = require('os');

class StreamDBManager {
  constructor() {
    this.ssh = new NodeSSH();
    this.connected = false;
    this.app = express();
    this.setupAPI();
  }

  async connect() {
    try {
      console.log('🔌 Connecting to StreamDB server...');
      
      await this.ssh.connect({
        host: '***********',
        username: 'root',
        privateKey: fs.readFileSync(path.join(os.homedir(), '.ssh', 'streamdb_ai'), 'utf8'),
        readyTimeout: 20000
      });
      
      this.connected = true;
      console.log('✅ Connected successfully');
      return true;
    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }
  }

  async exec(command, cwd = '/var/www/streamdb_onl_usr/data/www/streamdb.online') {
    if (!this.connected) await this.connect();
    
    try {
      const result = await this.ssh.execCommand(command, { cwd });
      console.log(`🔄 ${command}`);
      if (result.code === 0) {
        console.log('✅ Success');
        if (result.stdout) console.log('📤', result.stdout.substring(0, 200));
      } else {
        console.log('❌ Failed');
        if (result.stderr) console.log('📥', result.stderr.substring(0, 200));
      }
      return result;
    } catch (error) {
      console.error('💥', error.message);
      throw error;
    }
  }

  async diagnose() {
    console.log('\n🔍 Running diagnostics...\n');
    
    const checks = {};
    
    // System status
    try {
      const uptime = await this.exec('uptime');
      const df = await this.exec('df -h /');
      const free = await this.exec('free -h');
      checks.system = { uptime: uptime.stdout, disk: df.stdout, memory: free.stdout };
    } catch (e) { checks.system = { error: e.message }; }
    
    // Services
    try {
      const nginx = await this.exec('systemctl is-active nginx');
      const mysql = await this.exec('systemctl is-active mysql');
      const pm2 = await this.exec('pm2 jlist');
      checks.services = { 
        nginx: nginx.stdout.trim(), 
        mysql: mysql.stdout.trim(),
        pm2: pm2.stdout ? JSON.parse(pm2.stdout).length : 0
      };
    } catch (e) { checks.services = { error: e.message }; }
    
    // Application
    try {
      const files = await this.exec('ls -la');
      const env = await this.exec('ls -la server/.env');
      const port = await this.exec('ss -tlnp | grep :3001');
      checks.app = { 
        files: files.code === 0,
        env: env.code === 0,
        port3001: port.stdout.includes(':3001')
      };
    } catch (e) { checks.app = { error: e.message }; }
    
    return checks;
  }

  async autoFix() {
    console.log('\n🔧 Running automated fixes...\n');
    
    const fixes = [];
    
    try {
      // Fix permissions
      await this.exec('chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online');
      await this.exec('chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online');
      fixes.push('✅ Permissions fixed');
      
      // Create .env if missing
      const envCheck = await this.exec('ls server/.env');
      if (envCheck.code !== 0) {
        await this.exec(`cat > server/.env << 'EOF'
PORT=3001
NODE_ENV=production
DB_HOST=localhost
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_SOCKET=/var/run/mysqld/mysqld.sock
EOF`);
        await this.exec('chmod 600 server/.env');
        fixes.push('✅ Environment file created');
      }
      
      // Install dependencies
      await this.exec('cd server && npm install --production');
      fixes.push('✅ Dependencies installed');
      
      // Restart services
      await this.exec('systemctl restart nginx');
      await this.exec('systemctl restart mysql');
      fixes.push('✅ Services restarted');
      
      // Start PM2
      await this.exec('pm2 delete all || true');
      await this.exec('cd server && pm2 start index.js --name streamdb');
      await this.exec('pm2 save');
      fixes.push('✅ Application started');
      
    } catch (error) {
      fixes.push(`❌ Error: ${error.message}`);
    }
    
    return fixes;
  }

  setupAPI() {
    this.app.use(cors());
    this.app.use(express.json());

    this.app.get('/status', (req, res) => {
      res.json({ connected: this.connected, timestamp: new Date().toISOString() });
    });

    this.app.get('/diagnose', async (req, res) => {
      try {
        const result = await this.diagnose();
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/fix', async (req, res) => {
      try {
        const result = await this.autoFix();
        res.json({ fixes: result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/exec', async (req, res) => {
      try {
        const { command } = req.body;
        const result = await this.exec(command);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  async start() {
    await this.connect();
    
    const port = 3000;
    this.app.listen(port, () => {
      console.log(`\n🚀 StreamDB AI Management Server running on http://localhost:${port}`);
      console.log('\n📡 Available endpoints:');
      console.log('  GET  /status   - Connection status');
      console.log('  GET  /diagnose - Run diagnostics');
      console.log('  POST /fix      - Auto-fix issues');
      console.log('  POST /exec     - Execute command');
      console.log('\n✅ Ready for AI troubleshooting!');
    });
  }
}

const manager = new StreamDBManager();
manager.start().catch(console.error);
EOF

# Create client script
echo "🤖 Creating AI client..."
cat > client.js << 'EOF'
const http = require('http');

class AIClient {
  async request(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path,
        method,
        headers: { 'Content-Type': 'application/json' }
      };

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(body));
          } catch {
            resolve({ raw: body });
          }
        });
      });

      req.on('error', reject);
      if (data) req.write(JSON.stringify(data));
      req.end();
    });
  }

  async troubleshoot() {
    console.log('🤖 AI Troubleshooting Started\n');
    
    // Check connection
    try {
      const status = await this.request('/status');
      if (!status.connected) {
        console.log('❌ Not connected to server');
        return;
      }
      console.log('✅ Connected to server');
    } catch {
      console.log('❌ Management server not running');
      return;
    }
    
    // Run diagnostics
    console.log('\n🔍 Running diagnostics...');
    const diag = await this.request('/diagnose');
    
    // Analyze issues
    const issues = [];
    if (diag.services?.nginx !== 'active') issues.push('Nginx not running');
    if (diag.services?.mysql !== 'active') issues.push('MySQL not running');
    if (diag.services?.pm2 === 0) issues.push('No PM2 processes');
    if (!diag.app?.env) issues.push('Missing .env file');
    if (!diag.app?.port3001) issues.push('App not listening on port 3001');
    
    if (issues.length === 0) {
      console.log('🎉 No issues detected!');
      return;
    }
    
    console.log('\n❌ Issues found:');
    issues.forEach(issue => console.log(`  - ${issue}`));
    
    // Auto-fix
    console.log('\n🔧 Running auto-fix...');
    const fixes = await this.request('/fix', 'POST');
    
    console.log('\n✅ Fix results:');
    fixes.fixes?.forEach(fix => console.log(`  ${fix}`));
    
    // Verify
    console.log('\n🔍 Verifying fixes...');
    const postDiag = await this.request('/diagnose');
    
    const remaining = [];
    if (postDiag.services?.nginx !== 'active') remaining.push('Nginx still not running');
    if (postDiag.services?.mysql !== 'active') remaining.push('MySQL still not running');
    if (postDiag.services?.pm2 === 0) remaining.push('PM2 processes still not running');
    if (!postDiag.app?.port3001) remaining.push('App still not responding on port 3001');
    
    if (remaining.length === 0) {
      console.log('🎉 All issues resolved!');
    } else {
      console.log('\n⚠️  Remaining issues:');
      remaining.forEach(issue => console.log(`  - ${issue}`));
    }
  }
}

if (require.main === module) {
  const client = new AIClient();
  client.troubleshoot().catch(console.error);
}
EOF

# Create start script
echo "🚀 Creating start script..."
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting StreamDB AI Management Server..."
echo ""
echo "This will start the management server that allows AI direct access"
echo "to troubleshoot and fix your StreamDB server issues."
echo ""
node server.js
EOF

chmod +x start.sh

# Create the SSH config
echo "🔧 Configuring SSH..."
if [ -f ~/.ssh/config ]; then
    # Remove existing entry
    sed -i '/Host streamdb-ai/,/^$/d' ~/.ssh/config 2>/dev/null || true
fi

cat >> ~/.ssh/config << EOF

# StreamDB AI Access
Host streamdb-ai
    HostName ***********
    User root
    IdentityFile ~/.ssh/streamdb_ai
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10
    StrictHostKeyChecking no
EOF

chmod 600 ~/.ssh/config

echo ""
echo "🎉 Setup completed!"
echo ""
echo "📋 IMPORTANT: You need to add the SSH key to your server:"
echo ""
echo "Copy this public key:"
echo "----------------------------------------"
cat ~/.ssh/streamdb_ai.pub
echo "----------------------------------------"
echo ""
echo "And add it to your server by running this command on your server:"
echo "mkdir -p ~/.ssh && echo '$(cat ~/.ssh/streamdb_ai.pub)' >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys"
echo ""
echo "OR use ssh-copy-id:"
echo "ssh-copy-id -i ~/.ssh/streamdb_ai.pub root@***********"
echo ""
echo "After adding the key, start the management server:"
echo "cd ~/.streamdb-ai-access && ./start.sh"
echo ""
echo "Then I can troubleshoot directly with:"
echo "node client.js"
echo ""
echo "✅ Ready for AI troubleshooting!"
