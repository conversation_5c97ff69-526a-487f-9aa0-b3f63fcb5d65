{"name": "secure-remote-server-manager", "version": "1.0.0", "description": "Secure Remote Server Management Tool for AI Coding Assistants", "main": "remote-server-manager.js", "bin": {"remote-server-manager": "./remote-server-manager.js", "rsm": "./remote-server-manager.js"}, "scripts": {"start": "node remote-server-manager.js start", "start:api": "node remote-server-manager.js start --mode api", "start:cli": "node remote-server-manager.js start --mode cli", "diagnostics": "node remote-server-manager.js diagnostics", "emergency": "node remote-server-manager.js emergency", "install-deps": "npm install", "setup": "node scripts/setup.js", "test": "node scripts/test.js", "lint": "eslint *.js", "docs": "node scripts/generate-docs.js"}, "keywords": ["server-management", "ssh", "automation", "ai-assistant", "remote-administration", "devops", "monitoring", "security"], "author": "StreamDB Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "commander": "^11.1.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "node-ssh": "^13.1.0", "ssh2": "^1.15.0", "winston": "^3.11.0", "node-cron": "^3.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/streamdb/secure-remote-server-manager.git"}, "bugs": {"url": "https://github.com/streamdb/secure-remote-server-manager/issues"}, "homepage": "https://github.com/streamdb/secure-remote-server-manager#readme", "files": ["*.js", "config/", "scripts/", "docs/", "README.md", "LICENSE"], "preferGlobal": true, "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm64"]}