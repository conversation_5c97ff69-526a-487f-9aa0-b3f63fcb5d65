#!/bin/bash

# 🛑 Shutdown AI Access Script
# Completely stops all AI access to your server

echo "🛑 Shutting down AI access to StreamDB server..."
echo ""

# Stop the management server
echo "🔌 Stopping management server..."
pkill -f "node server.js" 2>/dev/null && echo "✅ Management server stopped" || echo "ℹ️  Management server was not running"

# Stop any Node.js processes in the AI access directory
echo "🔌 Stopping any related processes..."
pkill -f "streamdb-ai-access" 2>/dev/null && echo "✅ AI access processes stopped" || echo "ℹ️  No AI access processes found"

# Check if processes are still running
if pgrep -f "streamdb-ai-access" > /dev/null; then
    echo "⚠️  Some processes still running, force killing..."
    pkill -9 -f "streamdb-ai-access"
fi

echo ""
echo "🔒 AI Access Security Options:"
echo ""
echo "1. 🚪 TEMPORARY SHUTDOWN (recommended for now)"
echo "   - AI access is stopped"
echo "   - SSH key remains for future use"
echo "   - Can restart easily when needed"
echo ""
echo "2. 🔐 DISABLE SSH KEY (more secure)"
echo "   - Removes AI access completely"
echo "   - Would need to re-add key for future access"
echo ""
echo "3. 🗑️  COMPLETE REMOVAL"
echo "   - Removes all AI access files and keys"
echo "   - Would need full setup again"
echo ""

read -p "Choose option (1/2/3) or press Enter for option 1: " choice

case $choice in
    2)
        echo ""
        echo "🔐 Disabling SSH key on server..."
        echo "Run this command on your server to disable AI access:"
        echo ""
        echo "sed -i '/streamdb-ai-access/d' ~/.ssh/authorized_keys"
        echo ""
        echo "This removes the AI SSH key from your server."
        echo "To re-enable later, you'd need to add the key back."
        ;;
    3)
        echo ""
        echo "🗑️  Removing all AI access files..."
        
        # Remove SSH key
        if [ -f ~/.ssh/streamdb_ai ]; then
            rm ~/.ssh/streamdb_ai ~/.ssh/streamdb_ai.pub
            echo "✅ SSH keys removed"
        fi
        
        # Remove SSH config entry
        if [ -f ~/.ssh/config ]; then
            sed -i '/Host streamdb-ai/,/^$/d' ~/.ssh/config
            echo "✅ SSH config cleaned"
        fi
        
        # Remove management directory
        if [ -d ~/.streamdb-ai-access ]; then
            rm -rf ~/.streamdb-ai-access
            echo "✅ Management files removed"
        fi
        
        echo ""
        echo "🔐 Also run this on your server to remove the SSH key:"
        echo "sed -i '/streamdb-ai-access/d' ~/.ssh/authorized_keys"
        echo ""
        echo "✅ Complete removal finished"
        ;;
    *)
        echo ""
        echo "✅ AI access temporarily stopped"
        echo ""
        echo "📋 Status:"
        echo "  🛑 Management server: STOPPED"
        echo "  🔑 SSH key: Still available for future use"
        echo "  📁 Files: Preserved for easy restart"
        echo ""
        echo "🔄 To restart AI access later:"
        echo "  cd ~/.streamdb-ai-access && ./start.sh"
        ;;
esac

echo ""
echo "🔒 Current Security Status:"
echo "  🛑 AI Management Server: STOPPED"
echo "  🌐 Local ports: No longer listening"
echo "  🔐 Server access: Secured"
echo ""
echo "✅ AI access has been shut down successfully!"
