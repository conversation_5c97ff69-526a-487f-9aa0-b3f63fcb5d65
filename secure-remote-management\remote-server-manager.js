#!/usr/bin/env node

/**
 * 🔒 Secure Remote Server Management Tool
 * Main entry point for AI-assisted server management
 * 
 * Features:
 * - Unified interface for all server management operations
 * - CLI and API modes
 * - Comprehensive logging and monitoring
 * - Emergency recovery procedures
 * - Integration with AI coding assistants
 */

const fs = require('fs').promises;
const path = require('path');
const { program } = require('commander');
const chalk = require('chalk');
const inquirer = require('inquirer');

const SSHAuthManager = require('./ssh-auth-manager');
const SecureCommandExecutor = require('./command-executor');
const ServerDiagnostics = require('./server-diagnostics');
const AIIntegrationAPI = require('./ai-integration-api');

class RemoteServerManager {
  constructor(config = {}) {
    this.config = {
      serverIP: config.serverIP || '***********',
      serverUser: config.serverUser || 'root',
      configFile: config.configFile || './config/server-config.json',
      logLevel: config.logLevel || 'info',
      mode: config.mode || 'cli', // 'cli' or 'api'
      ...config
    };

    this.sshManager = null;
    this.commandExecutor = null;
    this.diagnostics = null;
    this.apiServer = null;
  }

  /**
   * Initialize the management tool
   */
  async initialize() {
    try {
      console.log(chalk.blue('🔒 Initializing Secure Remote Server Management Tool...'));
      
      // Load configuration
      await this.loadConfiguration();
      
      // Initialize components
      this.sshManager = new SSHAuthManager(this.config);
      this.commandExecutor = new SecureCommandExecutor(this.config);
      this.diagnostics = new ServerDiagnostics(this.config);
      
      // Initialize SSH connection
      console.log(chalk.yellow('🔐 Setting up SSH authentication...'));
      await this.sshManager.initialize();
      
      console.log(chalk.green('✅ Remote Server Manager initialized successfully'));
      return true;
      
    } catch (error) {
      console.error(chalk.red('❌ Initialization failed:'), error.message);
      throw error;
    }
  }

  /**
   * Load configuration from file
   */
  async loadConfiguration() {
    try {
      const configPath = path.resolve(this.config.configFile);
      const configData = await fs.readFile(configPath, 'utf8');
      const fileConfig = JSON.parse(configData);
      
      this.config = { ...this.config, ...fileConfig };
      console.log(chalk.green('📄 Configuration loaded from'), configPath);
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log(chalk.yellow('⚠️  Configuration file not found, using defaults'));
        await this.createDefaultConfig();
      } else {
        console.warn(chalk.yellow('⚠️  Failed to load configuration:'), error.message);
      }
    }
  }

  /**
   * Create default configuration file
   */
  async createDefaultConfig() {
    const defaultConfig = {
      serverIP: '***********',
      serverUser: 'root',
      maxExecutionTime: 300000,
      maxOutputSize: 10485760,
      auditLogPath: './logs/command-audit.log',
      backupDir: './backups',
      dryRun: false,
      alertThresholds: {
        cpuUsage: 80,
        memoryUsage: 85,
        diskUsage: 90,
        responseTime: 5000
      },
      apiConfig: {
        port: 3000,
        enableWebSocket: true,
        sessionTimeout: 3600000,
        maxConcurrentOperations: 5
      }
    };

    try {
      const configDir = path.dirname(this.config.configFile);
      await fs.mkdir(configDir, { recursive: true });
      await fs.writeFile(this.config.configFile, JSON.stringify(defaultConfig, null, 2));
      console.log(chalk.green('📄 Default configuration created at'), this.config.configFile);
    } catch (error) {
      console.warn(chalk.yellow('⚠️  Failed to create default configuration:'), error.message);
    }
  }

  /**
   * Start CLI mode
   */
  async startCLI() {
    console.log(chalk.blue('\n🖥️  Starting CLI mode...'));
    
    while (true) {
      try {
        const { action } = await inquirer.prompt([
          {
            type: 'list',
            name: 'action',
            message: 'What would you like to do?',
            choices: [
              { name: '🔍 Run Server Diagnostics', value: 'diagnostics' },
              { name: '⚡ Execute Command', value: 'command' },
              { name: '🔄 Restart Service', value: 'restart' },
              { name: '📊 Start Monitoring', value: 'monitor' },
              { name: '🔧 Manage Configuration', value: 'config' },
              { name: '💾 Database Operations', value: 'database' },
              { name: '🚀 Start API Server', value: 'api' },
              { name: '🚪 Exit', value: 'exit' }
            ]
          }
        ]);

        switch (action) {
          case 'diagnostics':
            await this.runDiagnosticsCLI();
            break;
          case 'command':
            await this.executeCommandCLI();
            break;
          case 'restart':
            await this.restartServiceCLI();
            break;
          case 'monitor':
            await this.startMonitoringCLI();
            break;
          case 'config':
            await this.manageConfigCLI();
            break;
          case 'database':
            await this.databaseOperationsCLI();
            break;
          case 'api':
            await this.startAPIServer();
            break;
          case 'exit':
            console.log(chalk.green('👋 Goodbye!'));
            return;
        }
      } catch (error) {
        console.error(chalk.red('❌ Error:'), error.message);
      }
    }
  }

  /**
   * Run diagnostics in CLI mode
   */
  async runDiagnosticsCLI() {
    console.log(chalk.blue('\n🔍 Running server diagnostics...'));
    
    const diagnostics = await this.diagnostics.runDiagnostics();
    
    console.log(chalk.green('\n📊 Diagnostics Results:'));
    console.log(chalk.cyan('Health Score:'), `${diagnostics.healthScore}%`);
    
    if (diagnostics.checks.system) {
      console.log(chalk.cyan('\n🖥️  System:'));
      console.log(`  Uptime: ${diagnostics.checks.system.uptime?.data || 'N/A'}`);
      console.log(`  OS: ${diagnostics.checks.system.os?.data?.PRETTY_NAME || 'N/A'}`);
    }
    
    if (diagnostics.checks.services) {
      console.log(chalk.cyan('\n🔧 Services:'));
      Object.entries(diagnostics.checks.services).forEach(([service, status]) => {
        const statusColor = status.status === 'running' ? chalk.green : chalk.red;
        console.log(`  ${service}: ${statusColor(status.status)}`);
      });
    }
    
    if (diagnostics.checks.resources) {
      console.log(chalk.cyan('\n📈 Resources:'));
      if (diagnostics.checks.resources.cpu) {
        console.log(`  CPU: ${diagnostics.checks.resources.cpu.usage}%`);
      }
      if (diagnostics.checks.resources.memory) {
        console.log(`  Memory: ${diagnostics.checks.resources.memory.usagePercent}%`);
      }
      if (diagnostics.checks.resources.disk) {
        console.log(`  Disk: ${diagnostics.checks.resources.disk.usagePercent}%`);
      }
    }
  }

  /**
   * Execute command in CLI mode
   */
  async executeCommandCLI() {
    const { command } = await inquirer.prompt([
      {
        type: 'input',
        name: 'command',
        message: 'Enter command to execute:',
        validate: (input) => input.trim() ? true : 'Command cannot be empty'
      }
    ]);

    const { dryRun } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'dryRun',
        message: 'Run in dry-run mode (safe preview)?',
        default: true
      }
    ]);

    try {
      console.log(chalk.blue('\n⚡ Executing command...'));
      const result = await this.commandExecutor.executeCommand(command, { dryRun });
      
      console.log(chalk.green('\n✅ Command executed successfully:'));
      console.log(chalk.cyan('Operation ID:'), result.operationId);
      console.log(chalk.cyan('Execution Time:'), `${result.executionTime}ms`);
      
      if (result.stdout) {
        console.log(chalk.cyan('\n📤 Output:'));
        console.log(result.stdout);
      }
      
      if (result.stderr) {
        console.log(chalk.yellow('\n⚠️  Warnings:'));
        console.log(result.stderr);
      }
      
    } catch (error) {
      console.error(chalk.red('\n❌ Command failed:'), error.message);
    }
  }

  /**
   * Restart service in CLI mode
   */
  async restartServiceCLI() {
    const { service } = await inquirer.prompt([
      {
        type: 'list',
        name: 'service',
        message: 'Which service would you like to restart?',
        choices: ['nginx', 'mysql', 'apache2', 'pm2 restart all']
      }
    ]);

    try {
      console.log(chalk.blue(`\n🔄 Restarting ${service}...`));
      
      const command = service.startsWith('pm2') ? service : `systemctl restart ${service}`;
      const result = await this.commandExecutor.executeCommand(command);
      
      console.log(chalk.green(`✅ ${service} restarted successfully`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to restart ${service}:`), error.message);
    }
  }

  /**
   * Start monitoring in CLI mode
   */
  async startMonitoringCLI() {
    console.log(chalk.blue('\n📊 Starting continuous monitoring...'));
    console.log(chalk.yellow('Press Ctrl+C to stop monitoring'));
    
    this.diagnostics.startMonitoring();
    
    // Keep the process alive
    process.on('SIGINT', () => {
      this.diagnostics.stopMonitoring();
      console.log(chalk.green('\n⏹️  Monitoring stopped'));
    });
  }

  /**
   * Start API server
   */
  async startAPIServer() {
    try {
      console.log(chalk.blue('\n🚀 Starting API server...'));
      
      this.apiServer = new AIIntegrationAPI({
        ...this.config,
        ...this.config.apiConfig
      });
      
      await this.apiServer.start();
      
      console.log(chalk.green('✅ API server started successfully'));
      console.log(chalk.cyan('🌐 Server URL:'), `http://localhost:${this.config.apiConfig?.port || 3000}`);
      console.log(chalk.yellow('Press Ctrl+C to stop the server'));
      
      // Keep the process alive
      process.on('SIGINT', async () => {
        console.log(chalk.blue('\n🛑 Stopping API server...'));
        await this.apiServer.stop();
        process.exit(0);
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to start API server:'), error.message);
    }
  }

  /**
   * Manage configuration in CLI mode
   */
  async manageConfigCLI() {
    console.log(chalk.blue('\n🔧 Configuration management not yet implemented in CLI'));
    console.log(chalk.yellow('Use API mode for full configuration management'));
  }

  /**
   * Database operations in CLI mode
   */
  async databaseOperationsCLI() {
    console.log(chalk.blue('\n💾 Database operations not yet implemented in CLI'));
    console.log(chalk.yellow('Use API mode for full database management'));
  }

  /**
   * Emergency recovery mode
   */
  async emergencyRecovery() {
    console.log(chalk.red('\n🚨 EMERGENCY RECOVERY MODE'));
    console.log(chalk.yellow('This mode provides basic recovery operations'));
    
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Select recovery action:',
        choices: [
          { name: '🔄 Restart all services', value: 'restart-all' },
          { name: '🔍 Check system status', value: 'status' },
          { name: '📋 View recent logs', value: 'logs' },
          { name: '🔙 Rollback last operation', value: 'rollback' },
          { name: '🚪 Exit recovery mode', value: 'exit' }
        ]
      }
    ]);

    switch (action) {
      case 'restart-all':
        await this.emergencyRestartAll();
        break;
      case 'status':
        await this.runDiagnosticsCLI();
        break;
      case 'logs':
        await this.viewRecentLogs();
        break;
      case 'rollback':
        await this.emergencyRollback();
        break;
      case 'exit':
        return;
    }
  }

  /**
   * Emergency restart all services
   */
  async emergencyRestartAll() {
    const services = ['nginx', 'mysql'];
    
    for (const service of services) {
      try {
        console.log(chalk.blue(`🔄 Restarting ${service}...`));
        await this.commandExecutor.executeCommand(`systemctl restart ${service}`);
        console.log(chalk.green(`✅ ${service} restarted`));
      } catch (error) {
        console.error(chalk.red(`❌ Failed to restart ${service}:`), error.message);
      }
    }
    
    try {
      console.log(chalk.blue('🔄 Restarting PM2 processes...'));
      await this.commandExecutor.executeCommand('pm2 restart all');
      console.log(chalk.green('✅ PM2 processes restarted'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to restart PM2:'), error.message);
    }
  }

  /**
   * View recent logs
   */
  async viewRecentLogs() {
    try {
      console.log(chalk.blue('\n📋 Recent system logs:'));
      const result = await this.commandExecutor.executeCommand('journalctl -n 20 --no-pager');
      console.log(result.stdout);
    } catch (error) {
      console.error(chalk.red('❌ Failed to retrieve logs:'), error.message);
    }
  }

  /**
   * Emergency rollback
   */
  async emergencyRollback() {
    console.log(chalk.yellow('\n🔙 Emergency rollback not yet implemented'));
    console.log(chalk.blue('Manual rollback steps:'));
    console.log('1. Check /tmp/ for recent backups');
    console.log('2. Restore configuration files');
    console.log('3. Restart affected services');
  }
}

// CLI setup
program
  .name('remote-server-manager')
  .description('Secure Remote Server Management Tool for AI Assistants')
  .version('1.0.0');

program
  .command('start')
  .description('Start the management tool in interactive mode')
  .option('-m, --mode <mode>', 'Mode: cli or api', 'cli')
  .option('-c, --config <file>', 'Configuration file path', './config/server-config.json')
  .action(async (options) => {
    const manager = new RemoteServerManager(options);
    
    try {
      await manager.initialize();
      
      if (options.mode === 'api') {
        await manager.startAPIServer();
      } else {
        await manager.startCLI();
      }
    } catch (error) {
      console.error(chalk.red('💥 Failed to start:'), error.message);
      process.exit(1);
    }
  });

program
  .command('emergency')
  .description('Start in emergency recovery mode')
  .action(async () => {
    const manager = new RemoteServerManager();
    
    try {
      await manager.initialize();
      await manager.emergencyRecovery();
    } catch (error) {
      console.error(chalk.red('💥 Emergency mode failed:'), error.message);
      process.exit(1);
    }
  });

program
  .command('diagnostics')
  .description('Run server diagnostics and exit')
  .action(async () => {
    const manager = new RemoteServerManager();
    
    try {
      await manager.initialize();
      await manager.runDiagnosticsCLI();
    } catch (error) {
      console.error(chalk.red('💥 Diagnostics failed:'), error.message);
      process.exit(1);
    }
  });

// Export for programmatic use
module.exports = RemoteServerManager;

// CLI execution
if (require.main === module) {
  program.parse();
}
