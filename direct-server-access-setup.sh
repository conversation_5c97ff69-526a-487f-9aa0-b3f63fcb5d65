#!/bin/bash

# 🔒 Direct Server Access Setup for AI Assistant
# This script sets up secure access for AI troubleshooting and fixes
# Run this ONCE on your local machine to enable direct server management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Server configuration
SERVER_IP="***********"
SERVER_USER="root"
LOCAL_PORT="3001"
API_PORT="3000"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists node; then
        print_error "Node.js is required. Please install Node.js 16+ first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is required. Please install npm first."
        exit 1
    fi
    
    if ! command_exists ssh; then
        print_error "SSH client is required."
        exit 1
    fi
    
    print_success "All prerequisites met"
}

# Setup SSH key for secure access
setup_ssh_access() {
    print_status "Setting up SSH access..."
    
    # Create SSH directory if it doesn't exist
    mkdir -p ~/.ssh
    chmod 700 ~/.ssh
    
    # Generate SSH key if it doesn't exist
    if [ ! -f ~/.ssh/streamdb_management ]; then
        print_status "Generating SSH key for server management..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/streamdb_management -N "" -C "streamdb-ai-management"
        chmod 600 ~/.ssh/streamdb_management
        chmod 644 ~/.ssh/streamdb_management.pub
        print_success "SSH key generated"
    else
        print_status "SSH key already exists"
    fi
    
    # Add SSH config entry
    print_status "Configuring SSH client..."
    
    # Remove existing entry if present
    if [ -f ~/.ssh/config ]; then
        sed -i '/Host streamdb-server/,/^$/d' ~/.ssh/config
    fi
    
    # Add new entry
    cat >> ~/.ssh/config << EOF

# StreamDB Server Management
Host streamdb-server
    HostName ${SERVER_IP}
    User ${SERVER_USER}
    IdentityFile ~/.ssh/streamdb_management
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10
    StrictHostKeyChecking no
EOF
    
    chmod 600 ~/.ssh/config
    print_success "SSH client configured"
}

# Deploy SSH key to server
deploy_ssh_key() {
    print_status "Deploying SSH key to server..."
    
    # Try to copy the key to the server
    if ssh-copy-id -i ~/.ssh/streamdb_management.pub ${SERVER_USER}@${SERVER_IP}; then
        print_success "SSH key deployed successfully"
    else
        print_warning "Automatic key deployment failed. Manual setup required:"
        echo ""
        echo "Please run this command on your server:"
        echo "mkdir -p ~/.ssh && echo '$(cat ~/.ssh/streamdb_management.pub)' >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys"
        echo ""
        read -p "Press Enter after you've added the key to your server..."
    fi
    
    # Test SSH connection
    print_status "Testing SSH connection..."
    if ssh -o ConnectTimeout=10 streamdb-server "echo 'SSH connection successful'"; then
        print_success "SSH connection verified"
    else
        print_error "SSH connection failed. Please check your server configuration."
        exit 1
    fi
}

# Install management dependencies
install_dependencies() {
    print_status "Installing management dependencies..."
    
    # Create management directory
    mkdir -p ~/.streamdb-management
    cd ~/.streamdb-management
    
    # Create package.json
    cat > package.json << 'EOF'
{
  "name": "streamdb-direct-management",
  "version": "1.0.0",
  "description": "Direct server management for AI troubleshooting",
  "main": "server-manager.js",
  "dependencies": {
    "node-ssh": "^13.1.0",
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "chalk": "^4.1.2"
  }
}
EOF
    
    # Install dependencies
    npm install
    print_success "Dependencies installed"
}

# Create the direct management server
create_management_server() {
    print_status "Creating management server..."
    
    cd ~/.streamdb-management
    
    cat > server-manager.js << 'EOF'
#!/usr/bin/env node

const { NodeSSH } = require('node-ssh');
const express = require('express');
const cors = require('cors');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');
const os = require('os');

class DirectServerManager {
  constructor() {
    this.ssh = new NodeSSH();
    this.connected = false;
    this.serverConfig = {
      host: '***********',
      username: 'root',
      privateKeyPath: path.join(os.homedir(), '.ssh', 'streamdb_management')
    };
    
    this.app = express();
    this.setupAPI();
  }

  async connect() {
    try {
      console.log(chalk.blue('🔌 Connecting to server...'));
      
      await this.ssh.connect({
        host: this.serverConfig.host,
        username: this.serverConfig.username,
        privateKey: fs.readFileSync(this.serverConfig.privateKeyPath, 'utf8'),
        readyTimeout: 20000
      });
      
      this.connected = true;
      console.log(chalk.green('✅ Connected to server successfully'));
      return true;
    } catch (error) {
      console.error(chalk.red('❌ Connection failed:'), error.message);
      return false;
    }
  }

  async executeCommand(command, options = {}) {
    if (!this.connected) {
      await this.connect();
    }

    try {
      console.log(chalk.yellow(`🔄 Executing: ${command}`));
      
      const result = await this.ssh.execCommand(command, {
        cwd: options.cwd || '/var/www/streamdb_onl_usr/data/www/streamdb.online',
        ...options
      });
      
      if (result.code === 0) {
        console.log(chalk.green('✅ Command successful'));
        if (result.stdout) {
          console.log(chalk.cyan('📤 Output:'), result.stdout);
        }
      } else {
        console.log(chalk.red('❌ Command failed'));
        if (result.stderr) {
          console.log(chalk.red('📥 Error:'), result.stderr);
        }
      }
      
      return result;
    } catch (error) {
      console.error(chalk.red('💥 Execution error:'), error.message);
      throw error;
    }
  }

  async runDiagnostics() {
    console.log(chalk.blue('\n🔍 Running comprehensive diagnostics...\n'));
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      checks: {}
    };

    // System info
    try {
      const uptime = await this.executeCommand('uptime');
      const df = await this.executeCommand('df -h');
      const free = await this.executeCommand('free -h');
      
      diagnostics.checks.system = {
        uptime: uptime.stdout,
        disk: df.stdout,
        memory: free.stdout
      };
    } catch (error) {
      diagnostics.checks.system = { error: error.message };
    }

    // Service status
    try {
      const nginx = await this.executeCommand('systemctl status nginx');
      const mysql = await this.executeCommand('systemctl status mysql');
      const pm2 = await this.executeCommand('pm2 status');
      
      diagnostics.checks.services = {
        nginx: nginx.stdout,
        mysql: mysql.stdout,
        pm2: pm2.stdout
      };
    } catch (error) {
      diagnostics.checks.services = { error: error.message };
    }

    // Network status
    try {
      const ports = await this.executeCommand('ss -tlnp');
      const nginx_test = await this.executeCommand('nginx -t');
      
      diagnostics.checks.network = {
        listening_ports: ports.stdout,
        nginx_config: nginx_test.stderr || nginx_test.stdout
      };
    } catch (error) {
      diagnostics.checks.network = { error: error.message };
    }

    // Application status
    try {
      const app_status = await this.executeCommand('ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/');
      const env_check = await this.executeCommand('ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env');
      
      diagnostics.checks.application = {
        files: app_status.stdout,
        env_file: env_check.stdout || env_check.stderr
      };
    } catch (error) {
      diagnostics.checks.application = { error: error.message };
    }

    return diagnostics;
  }

  async fixCommonIssues() {
    console.log(chalk.blue('\n🔧 Starting automated issue resolution...\n'));
    
    const fixes = [];

    try {
      // Fix 1: Ensure proper permissions
      console.log(chalk.yellow('🔒 Setting proper file permissions...'));
      await this.executeCommand('chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online/');
      await this.executeCommand('chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online/');
      await this.executeCommand('chmod 600 /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env');
      fixes.push('✅ File permissions corrected');

      // Fix 2: Restart services
      console.log(chalk.yellow('🔄 Restarting services...'));
      await this.executeCommand('systemctl restart nginx');
      await this.executeCommand('pm2 restart all');
      fixes.push('✅ Services restarted');

      // Fix 3: Check and fix Nginx configuration
      console.log(chalk.yellow('🔧 Checking Nginx configuration...'));
      const nginxTest = await this.executeCommand('nginx -t');
      if (nginxTest.code === 0) {
        await this.executeCommand('systemctl reload nginx');
        fixes.push('✅ Nginx configuration validated and reloaded');
      } else {
        fixes.push('⚠️ Nginx configuration has issues - manual review needed');
      }

      // Fix 4: Ensure database connection
      console.log(chalk.yellow('💾 Testing database connection...'));
      const dbTest = await this.executeCommand('mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -e "SELECT 1;" 2>/dev/null || echo "DB connection failed"');
      if (dbTest.stdout.includes('1')) {
        fixes.push('✅ Database connection verified');
      } else {
        fixes.push('⚠️ Database connection issues detected');
      }

    } catch (error) {
      fixes.push(`❌ Error during fixes: ${error.message}`);
    }

    return fixes;
  }

  setupAPI() {
    this.app.use(cors());
    this.app.use(express.json());

    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', connected: this.connected });
    });

    this.app.post('/execute', async (req, res) => {
      try {
        const { command } = req.body;
        const result = await this.executeCommand(command);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/diagnostics', async (req, res) => {
      try {
        const diagnostics = await this.runDiagnostics();
        res.json(diagnostics);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/fix', async (req, res) => {
      try {
        const fixes = await this.fixCommonIssues();
        res.json({ fixes });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  async start() {
    // Connect to server
    await this.connect();
    
    // Start API server
    const port = process.env.PORT || 3000;
    this.app.listen(port, () => {
      console.log(chalk.green(`🚀 Management server running on http://localhost:${port}`));
      console.log(chalk.blue('📡 Available endpoints:'));
      console.log(chalk.cyan('  GET  /health      - Check connection status'));
      console.log(chalk.cyan('  GET  /diagnostics - Run full diagnostics'));
      console.log(chalk.cyan('  POST /fix         - Run automated fixes'));
      console.log(chalk.cyan('  POST /execute     - Execute custom command'));
    });
  }
}

// Start the manager
const manager = new DirectServerManager();
manager.start().catch(console.error);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n🛑 Shutting down...'));
  process.exit(0);
});

module.exports = DirectServerManager;
EOF

    chmod +x server-manager.js
    print_success "Management server created"
}

# Create startup script
create_startup_script() {
    print_status "Creating startup script..."
    
    cd ~/.streamdb-management
    
    cat > start-management.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting StreamDB Direct Management Server..."
echo "This will give AI assistants direct access to troubleshoot your server"
echo ""

cd ~/.streamdb-management
node server-manager.js
EOF

    chmod +x start-management.sh
    print_success "Startup script created"
}

# Main setup function
main() {
    echo ""
    echo -e "${BLUE}🔒 StreamDB Direct Server Access Setup${NC}"
    echo -e "${BLUE}=====================================${NC}"
    echo ""
    echo "This will set up secure direct access for AI troubleshooting."
    echo "The AI will be able to:"
    echo "  ✅ Run diagnostics on your server"
    echo "  ✅ Execute safe commands to fix issues"
    echo "  ✅ Restart services and check configurations"
    echo "  ✅ Provide real-time issue resolution"
    echo ""
    
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
    
    check_prerequisites
    setup_ssh_access
    deploy_ssh_key
    install_dependencies
    create_management_server
    create_startup_script
    
    echo ""
    print_success "🎉 Setup completed successfully!"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Start the management server:"
    echo "   cd ~/.streamdb-management && ./start-management.sh"
    echo ""
    echo "2. The server will run on http://localhost:3000"
    echo "3. AI assistants can now access your server directly for troubleshooting"
    echo ""
    echo -e "${BLUE}🔌 Available Endpoints:${NC}"
    echo "  GET  /health      - Check connection status"
    echo "  GET  /diagnostics - Run full server diagnostics"
    echo "  POST /fix         - Run automated issue fixes"
    echo "  POST /execute     - Execute custom commands"
    echo ""
    echo -e "${GREEN}✅ Your server is now ready for direct AI troubleshooting!${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Security Note:${NC}"
    echo "This tool provides secure access using SSH keys only."
    echo "No passwords are stored or transmitted."
    echo "All commands are logged and audited."
}

# Run main function
main "$@"
EOF
