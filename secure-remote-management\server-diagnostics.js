#!/usr/bin/env node

/**
 * 🔍 Server Diagnostics and Health Monitoring
 * Comprehensive server health checks and real-time monitoring
 * 
 * Features:
 * - Service status monitoring (PM2, MySQL, Nginx, FastPanel)
 * - Port connectivity and network diagnostics
 * - System resource monitoring
 * - Real-time log analysis
 * - Performance metrics collection
 */

const SecureCommandExecutor = require('./command-executor');
const fs = require('fs').promises;
const path = require('path');

class ServerDiagnostics {
  constructor(config = {}) {
    this.config = {
      serverIP: config.serverIP || '***********',
      serverUser: config.serverUser || 'root',
      checkInterval: config.checkInterval || 30000, // 30 seconds
      alertThresholds: {
        cpuUsage: 80,
        memoryUsage: 85,
        diskUsage: 90,
        responseTime: 5000
      },
      ...config
    };

    this.executor = new SecureCommandExecutor(config);
    this.healthHistory = [];
    this.isMonitoring = false;
  }

  /**
   * Run comprehensive server diagnostics
   */
  async runDiagnostics() {
    console.log('🔍 Running comprehensive server diagnostics...');
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      server: this.config.serverIP,
      checks: {}
    };

    try {
      // System information
      diagnostics.checks.system = await this.checkSystemInfo();
      
      // Service status
      diagnostics.checks.services = await this.checkServices();
      
      // Network connectivity
      diagnostics.checks.network = await this.checkNetworkConnectivity();
      
      // Database health
      diagnostics.checks.database = await this.checkDatabaseHealth();
      
      // Application status
      diagnostics.checks.applications = await this.checkApplications();
      
      // Resource usage
      diagnostics.checks.resources = await this.checkResourceUsage();
      
      // Security status
      diagnostics.checks.security = await this.checkSecurityStatus();

      // Calculate overall health score
      diagnostics.healthScore = this.calculateHealthScore(diagnostics.checks);
      
      console.log('✅ Diagnostics completed successfully');
      return diagnostics;
      
    } catch (error) {
      console.error('❌ Diagnostics failed:', error.message);
      diagnostics.error = error.message;
      return diagnostics;
    }
  }

  /**
   * Check system information
   */
  async checkSystemInfo() {
    const checks = {};
    
    try {
      // System uptime
      const uptimeResult = await this.executor.executeCommand('uptime');
      checks.uptime = {
        status: 'healthy',
        data: uptimeResult.stdout,
        loadAverage: this.parseLoadAverage(uptimeResult.stdout)
      };

      // OS information
      const osResult = await this.executor.executeCommand('cat /etc/os-release');
      checks.os = {
        status: 'healthy',
        data: this.parseOSInfo(osResult.stdout)
      };

      // Kernel version
      const kernelResult = await this.executor.executeCommand('uname -r');
      checks.kernel = {
        status: 'healthy',
        version: kernelResult.stdout.trim()
      };

    } catch (error) {
      checks.error = error.message;
    }

    return checks;
  }

  /**
   * Check service status
   */
  async checkServices() {
    const services = ['nginx', 'mysql', 'ssh'];
    const checks = {};

    for (const service of services) {
      try {
        const result = await this.executor.executeCommand(`systemctl status ${service}`);
        checks[service] = {
          status: result.stdout.includes('active (running)') ? 'running' : 'stopped',
          details: this.parseServiceStatus(result.stdout)
        };
      } catch (error) {
        checks[service] = {
          status: 'error',
          error: error.message
        };
      }
    }

    // Check PM2 processes
    try {
      const pm2Result = await this.executor.executeCommand('pm2 jlist');
      checks.pm2 = {
        status: 'healthy',
        processes: JSON.parse(pm2Result.stdout || '[]')
      };
    } catch (error) {
      checks.pm2 = {
        status: 'error',
        error: error.message
      };
    }

    return checks;
  }

  /**
   * Check network connectivity
   */
  async checkNetworkConnectivity() {
    const ports = [22, 80, 443, 3001, 8888, 9000];
    const checks = {};

    // Port connectivity
    checks.ports = {};
    for (const port of ports) {
      try {
        const result = await this.executor.executeCommand(`ss -tlnp | grep :${port}`);
        checks.ports[port] = {
          status: result.stdout ? 'listening' : 'closed',
          details: result.stdout.trim()
        };
      } catch (error) {
        checks.ports[port] = {
          status: 'error',
          error: error.message
        };
      }
    }

    // External connectivity
    try {
      const pingResult = await this.executor.executeCommand('ping -c 3 8.8.8.8');
      checks.internet = {
        status: pingResult.stdout.includes('3 received') ? 'connected' : 'limited',
        details: this.parsePingResult(pingResult.stdout)
      };
    } catch (error) {
      checks.internet = {
        status: 'disconnected',
        error: error.message
      };
    }

    return checks;
  }

  /**
   * Check database health
   */
  async checkDatabaseHealth() {
    const checks = {};

    try {
      // MySQL service status
      const mysqlStatus = await this.executor.executeCommand('systemctl status mysql');
      checks.service = {
        status: mysqlStatus.stdout.includes('active (running)') ? 'running' : 'stopped'
      };

      // Database connectivity (using socket)
      const connectTest = await this.executor.executeCommand(
        'mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -pYOUR_PASSWORD -e "SELECT 1;" 2>/dev/null'
      );
      checks.connectivity = {
        status: connectTest.stdout.includes('1') ? 'connected' : 'failed'
      };

      // Database size and tables
      const dbInfo = await this.executor.executeCommand(
        'mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -pYOUR_PASSWORD -e "SELECT table_schema, COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = \'streamdb_database\' GROUP BY table_schema;" 2>/dev/null'
      );
      checks.database = {
        status: 'healthy',
        info: this.parseDatabaseInfo(dbInfo.stdout)
      };

    } catch (error) {
      checks.error = error.message;
    }

    return checks;
  }

  /**
   * Check application status
   */
  async checkApplications() {
    const checks = {};

    try {
      // StreamDB application
      const streamdbStatus = await this.executor.executeCommand('pm2 show streamdb-online 2>/dev/null || pm2 show index');
      checks.streamdb = {
        status: streamdbStatus.stdout.includes('online') ? 'running' : 'stopped',
        details: this.parseProcessInfo(streamdbStatus.stdout)
      };

      // Webhook server
      const webhookStatus = await this.executor.executeCommand('pm2 show webhook-server 2>/dev/null');
      checks.webhook = {
        status: webhookStatus.stdout.includes('online') ? 'running' : 'stopped',
        details: this.parseProcessInfo(webhookStatus.stdout)
      };

      // FastPanel status
      const fastpanelCheck = await this.executor.executeCommand('curl -I http://localhost:7777 2>/dev/null');
      checks.fastpanel = {
        status: fastpanelCheck.stdout.includes('200 OK') ? 'running' : 'stopped'
      };

    } catch (error) {
      checks.error = error.message;
    }

    return checks;
  }

  /**
   * Check resource usage
   */
  async checkResourceUsage() {
    const checks = {};

    try {
      // CPU usage
      const cpuResult = await this.executor.executeCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
      checks.cpu = {
        usage: parseFloat(cpuResult.stdout.trim()),
        status: parseFloat(cpuResult.stdout.trim()) > this.config.alertThresholds.cpuUsage ? 'warning' : 'healthy'
      };

      // Memory usage
      const memResult = await this.executor.executeCommand('free -m');
      const memInfo = this.parseMemoryInfo(memResult.stdout);
      checks.memory = {
        ...memInfo,
        status: memInfo.usagePercent > this.config.alertThresholds.memoryUsage ? 'warning' : 'healthy'
      };

      // Disk usage
      const diskResult = await this.executor.executeCommand('df -h /');
      const diskInfo = this.parseDiskInfo(diskResult.stdout);
      checks.disk = {
        ...diskInfo,
        status: diskInfo.usagePercent > this.config.alertThresholds.diskUsage ? 'warning' : 'healthy'
      };

    } catch (error) {
      checks.error = error.message;
    }

    return checks;
  }

  /**
   * Check security status
   */
  async checkSecurityStatus() {
    const checks = {};

    try {
      // Firewall status
      const ufwStatus = await this.executor.executeCommand('ufw status');
      checks.firewall = {
        status: ufwStatus.stdout.includes('Status: active') ? 'active' : 'inactive',
        rules: this.parseFirewallRules(ufwStatus.stdout)
      };

      // SSH configuration
      const sshConfig = await this.executor.executeCommand('grep -E "^(PermitRootLogin|PasswordAuthentication|Port)" /etc/ssh/sshd_config');
      checks.ssh = {
        status: 'configured',
        config: this.parseSSHConfig(sshConfig.stdout)
      };

      // Failed login attempts
      const failedLogins = await this.executor.executeCommand('grep "Failed password" /var/log/auth.log | tail -10');
      checks.security = {
        failedLogins: failedLogins.stdout.split('\n').filter(line => line.trim()).length,
        status: 'monitored'
      };

    } catch (error) {
      checks.error = error.message;
    }

    return checks;
  }

  /**
   * Calculate overall health score
   */
  calculateHealthScore(checks) {
    let totalScore = 0;
    let maxScore = 0;

    const scoreWeights = {
      system: 10,
      services: 25,
      network: 15,
      database: 20,
      applications: 20,
      resources: 15,
      security: 10
    };

    for (const [category, weight] of Object.entries(scoreWeights)) {
      maxScore += weight;
      
      if (checks[category] && !checks[category].error) {
        totalScore += weight;
      }
    }

    return Math.round((totalScore / maxScore) * 100);
  }

  /**
   * Parse helper methods
   */
  parseLoadAverage(uptimeOutput) {
    const match = uptimeOutput.match(/load average: ([\d.]+), ([\d.]+), ([\d.]+)/);
    return match ? {
      '1min': parseFloat(match[1]),
      '5min': parseFloat(match[2]),
      '15min': parseFloat(match[3])
    } : null;
  }

  parseOSInfo(osRelease) {
    const info = {};
    osRelease.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        info[key] = value.replace(/"/g, '');
      }
    });
    return info;
  }

  parseServiceStatus(statusOutput) {
    const lines = statusOutput.split('\n');
    return {
      active: statusOutput.includes('active (running)'),
      enabled: statusOutput.includes('enabled'),
      since: lines.find(line => line.includes('since'))?.trim()
    };
  }

  parsePingResult(pingOutput) {
    const match = pingOutput.match(/(\d+) packets transmitted, (\d+) received/);
    return match ? {
      transmitted: parseInt(match[1]),
      received: parseInt(match[2]),
      loss: ((parseInt(match[1]) - parseInt(match[2])) / parseInt(match[1])) * 100
    } : null;
  }

  parseDatabaseInfo(dbOutput) {
    const lines = dbOutput.split('\n').filter(line => line.trim());
    return lines.length > 1 ? {
      database: lines[1].split('\t')[0],
      tableCount: parseInt(lines[1].split('\t')[1])
    } : null;
  }

  parseProcessInfo(processOutput) {
    return {
      online: processOutput.includes('online'),
      uptime: processOutput.match(/uptime\s+│\s+(.+)/)?.[1],
      restarts: processOutput.match(/restarts\s+│\s+(\d+)/)?.[1]
    };
  }

  parseMemoryInfo(memOutput) {
    const lines = memOutput.split('\n');
    const memLine = lines.find(line => line.startsWith('Mem:'));
    if (memLine) {
      const parts = memLine.split(/\s+/);
      const total = parseInt(parts[1]);
      const used = parseInt(parts[2]);
      return {
        total: total,
        used: used,
        free: parseInt(parts[3]),
        usagePercent: Math.round((used / total) * 100)
      };
    }
    return null;
  }

  parseDiskInfo(diskOutput) {
    const lines = diskOutput.split('\n');
    const diskLine = lines.find(line => line.includes('/'));
    if (diskLine) {
      const parts = diskLine.split(/\s+/);
      return {
        total: parts[1],
        used: parts[2],
        available: parts[3],
        usagePercent: parseInt(parts[4].replace('%', ''))
      };
    }
    return null;
  }

  parseFirewallRules(ufwOutput) {
    return ufwOutput.split('\n')
      .filter(line => line.includes('ALLOW'))
      .map(line => line.trim());
  }

  parseSSHConfig(sshOutput) {
    const config = {};
    sshOutput.split('\n').forEach(line => {
      const [key, value] = line.split(/\s+/);
      if (key && value) {
        config[key] = value;
      }
    });
    return config;
  }

  /**
   * Start continuous monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️  Monitoring already active');
      return;
    }

    console.log('🔄 Starting continuous server monitoring...');
    this.isMonitoring = true;

    this.monitoringInterval = setInterval(async () => {
      try {
        const diagnostics = await this.runDiagnostics();
        this.healthHistory.push(diagnostics);
        
        // Keep only last 100 entries
        if (this.healthHistory.length > 100) {
          this.healthHistory.shift();
        }

        // Check for alerts
        this.checkAlerts(diagnostics);
        
      } catch (error) {
        console.error('Monitoring error:', error.message);
      }
    }, this.config.checkInterval);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.isMonitoring = false;
      console.log('⏹️  Monitoring stopped');
    }
  }

  /**
   * Check for alert conditions
   */
  checkAlerts(diagnostics) {
    const alerts = [];

    if (diagnostics.healthScore < 70) {
      alerts.push(`Low health score: ${diagnostics.healthScore}%`);
    }

    if (diagnostics.checks.resources?.cpu?.status === 'warning') {
      alerts.push(`High CPU usage: ${diagnostics.checks.resources.cpu.usage}%`);
    }

    if (diagnostics.checks.resources?.memory?.status === 'warning') {
      alerts.push(`High memory usage: ${diagnostics.checks.resources.memory.usagePercent}%`);
    }

    if (alerts.length > 0) {
      console.log('🚨 ALERTS:', alerts.join(', '));
    }
  }

  /**
   * Get health history
   */
  getHealthHistory() {
    return this.healthHistory;
  }
}

module.exports = ServerDiagnostics;

// CLI usage
if (require.main === module) {
  const diagnostics = new ServerDiagnostics();
  
  diagnostics.runDiagnostics()
    .then((result) => {
      console.log('📊 Diagnostics Results:');
      console.log(JSON.stringify(result, null, 2));
    })
    .catch((error) => {
      console.error('💥 Diagnostics failed:', error.message);
      process.exit(1);
    });
}
