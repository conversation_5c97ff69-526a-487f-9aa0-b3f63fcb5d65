#!/usr/bin/env node

/**
 * 🔧 FastPanel Specific Diagnostics and Fixes
 * Comprehensive FastPanel troubleshooting and repair
 */

const http = require('http');

class FastPanelFixer {
  async request(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path,
        method,
        headers: { 'Content-Type': 'application/json' }
      };

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(body));
          } catch {
            resolve({ raw: body });
          }
        });
      });

      req.on('error', reject);
      if (data) req.write(JSON.stringify(data));
      req.end();
    });
  }

  async exec(command) {
    try {
      const result = await this.request('/exec', 'POST', { command });
      console.log(`🔄 ${command}`);
      
      if (result.code === 0) {
        console.log('✅ Success');
        if (result.stdout && result.stdout.trim()) {
          console.log('📤 Output:', result.stdout.trim());
        }
      } else {
        console.log('❌ Failed');
        if (result.stderr && result.stderr.trim()) {
          console.log('📥 Error:', result.stderr.trim());
        }
      }
      
      return result;
    } catch (error) {
      console.error('💥 Request failed:', error.message);
      return { code: -1, error: error.message };
    }
  }

  async diagnoseFastPanel() {
    console.log('🔍 FastPanel Comprehensive Diagnostics\n');
    
    const diagnostics = {};
    
    // Check FastPanel ports
    console.log('🌐 Checking FastPanel ports...');
    const ports = await this.exec('ss -tlnp | grep -E ":(7777|8888|5502)"');
    diagnostics.ports = {
      listening: ports.stdout || 'No FastPanel ports found',
      status: ports.code === 0 ? 'found' : 'not_found'
    };
    
    // Check FastPanel service
    console.log('\n🔧 Checking FastPanel service...');
    const service = await this.exec('systemctl status fastpanel2 || systemctl status fastpanel');
    diagnostics.service = {
      status: service.stdout || service.stderr || 'Service not found',
      running: service.stdout?.includes('active (running)') || false
    };
    
    // Check FastPanel processes
    console.log('\n⚙️  Checking FastPanel processes...');
    const processes = await this.exec('ps aux | grep -i fastpanel | grep -v grep');
    diagnostics.processes = {
      found: processes.stdout || 'No FastPanel processes found',
      running: processes.code === 0
    };
    
    // Check FastPanel installation
    console.log('\n📁 Checking FastPanel installation...');
    const installation = await this.exec('ls -la /usr/local/fastpanel2/ || ls -la /usr/local/fastpanel/');
    diagnostics.installation = {
      path: installation.stdout || 'FastPanel not found in standard locations',
      exists: installation.code === 0
    };
    
    // Check FastPanel configuration
    console.log('\n⚙️  Checking FastPanel configuration...');
    const config = await this.exec('find /etc -name "*fastpanel*" -type f 2>/dev/null | head -5');
    diagnostics.config = {
      files: config.stdout || 'No FastPanel config files found',
      found: config.code === 0 && config.stdout?.trim()
    };
    
    // Check if FastPanel is installed via package manager
    console.log('\n📦 Checking FastPanel package installation...');
    const package_check = await this.exec('dpkg -l | grep fastpanel || rpm -qa | grep fastpanel');
    diagnostics.package = {
      installed: package_check.stdout || 'Not installed via package manager',
      found: package_check.code === 0
    };
    
    // Check for FastPanel in common locations
    console.log('\n🔍 Searching for FastPanel in common locations...');
    const search = await this.exec('find /opt /usr/local /var -name "*fastpanel*" -type d 2>/dev/null | head -10');
    diagnostics.search = {
      locations: search.stdout || 'No FastPanel directories found',
      found: search.code === 0 && search.stdout?.trim()
    };
    
    return diagnostics;
  }

  async fixFastPanel() {
    console.log('\n🔧 FastPanel Automated Fixes\n');
    
    const fixes = [];
    
    // First, let's see what we're working with
    const diag = await this.diagnoseFastPanel();
    
    // Fix 1: Check if FastPanel is actually installed
    if (!diag.installation.exists && !diag.package.found && !diag.search.found) {
      console.log('❌ FastPanel does not appear to be installed on this server');
      console.log('🔍 Let me check if this is an Alexhost server with FastPanel...');
      
      // Check for Alexhost-specific FastPanel
      const alexhost_check = await this.exec('ls -la /usr/local/mgr5/ || ls -la /usr/local/ispmgr/');
      if (alexhost_check.code === 0) {
        console.log('✅ Found ISPmanager/mgr5 (Alexhost control panel)');
        fixes.push('Found ISPmanager instead of FastPanel');
        
        // Check ISPmanager status
        await this.exec('systemctl status ispmgr || /usr/local/mgr5/sbin/mgrctl -m ispmgr');
        
        return fixes;
      }
      
      fixes.push('❌ FastPanel not found - may need manual installation');
      return fixes;
    }
    
    // Fix 2: Try to start FastPanel service
    console.log('🔄 Attempting to start FastPanel service...');
    const start_service = await this.exec('systemctl start fastpanel2 || systemctl start fastpanel');
    if (start_service.code === 0) {
      fixes.push('✅ FastPanel service started');
    } else {
      fixes.push('⚠️ Could not start FastPanel service');
    }
    
    // Fix 3: Check and fix FastPanel configuration
    console.log('🔧 Checking FastPanel configuration...');
    
    // Look for FastPanel config
    const config_search = await this.exec('find /etc -name "*fastpanel*" 2>/dev/null');
    if (config_search.stdout) {
      console.log('📁 Found FastPanel config files');
      fixes.push('✅ FastPanel configuration files found');
    }
    
    // Fix 4: Check if FastPanel is bound to correct ports
    console.log('🌐 Checking port bindings...');
    const netstat = await this.exec('netstat -tlnp | grep -E ":(7777|8888)"');
    if (netstat.code === 0) {
      fixes.push('✅ FastPanel ports are bound');
    } else {
      // Try to restart networking or FastPanel
      console.log('🔄 Attempting to fix port bindings...');
      await this.exec('systemctl restart fastpanel2 || systemctl restart fastpanel');
      fixes.push('⚠️ Attempted to restart FastPanel for port binding');
    }
    
    // Fix 5: Check firewall rules
    console.log('🔥 Checking firewall for FastPanel ports...');
    const ufw_status = await this.exec('ufw status | grep -E "(7777|8888)"');
    if (ufw_status.code !== 0) {
      console.log('🔓 Opening FastPanel ports in firewall...');
      await this.exec('ufw allow 7777');
      await this.exec('ufw allow 8888');
      fixes.push('✅ Opened FastPanel ports in firewall');
    } else {
      fixes.push('✅ FastPanel ports already open in firewall');
    }
    
    // Fix 6: Test FastPanel web interface
    console.log('🌐 Testing FastPanel web interface...');
    const web_test = await this.exec('curl -I http://localhost:7777 2>/dev/null || curl -I http://localhost:8888 2>/dev/null');
    if (web_test.code === 0) {
      fixes.push('✅ FastPanel web interface responding');
    } else {
      fixes.push('❌ FastPanel web interface not responding');
    }
    
    // Fix 7: Check for proxy configuration issues
    console.log('🔄 Checking proxy configuration...');
    const nginx_fastpanel = await this.exec('grep -r "fastpanel\\|7777\\|8888" /etc/nginx/ 2>/dev/null');
    if (nginx_fastpanel.code === 0) {
      fixes.push('✅ Found FastPanel proxy configuration in Nginx');
    } else {
      fixes.push('⚠️ No FastPanel proxy configuration found in Nginx');
    }
    
    return fixes;
  }

  async createFastPanelProxy() {
    console.log('\n🔄 Creating FastPanel Proxy Configuration\n');
    
    // Create Nginx proxy configuration for FastPanel
    const proxy_config = `
# FastPanel Proxy Configuration
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    location / {
        proxy_pass http://127.0.0.1:7777;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

server {
    listen 443 ssl;
    server_name fastpanel.streamdb.online;
    
    # SSL configuration would go here
    
    location / {
        proxy_pass http://127.0.0.1:7777;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
`;

    console.log('📝 Creating FastPanel proxy configuration...');
    const create_config = await this.exec(`cat > /etc/nginx/sites-available/fastpanel-proxy << 'EOF'${proxy_config}EOF`);
    
    if (create_config.code === 0) {
      console.log('✅ FastPanel proxy config created');
      
      // Enable the site
      await this.exec('ln -sf /etc/nginx/sites-available/fastpanel-proxy /etc/nginx/sites-enabled/');
      
      // Test Nginx configuration
      const nginx_test = await this.exec('nginx -t');
      if (nginx_test.code === 0) {
        console.log('✅ Nginx configuration test passed');
        
        // Reload Nginx
        await this.exec('systemctl reload nginx');
        console.log('✅ Nginx reloaded with FastPanel proxy');
        
        return true;
      } else {
        console.log('❌ Nginx configuration test failed');
        return false;
      }
    } else {
      console.log('❌ Failed to create FastPanel proxy configuration');
      return false;
    }
  }

  async comprehensiveFastPanelFix() {
    console.log('🚀 Comprehensive FastPanel Diagnosis and Fix\n');
    
    // Step 1: Diagnose
    const diagnostics = await this.diagnoseFastPanel();
    
    console.log('\n📊 Diagnostics Summary:');
    console.log('========================');
    console.log(`Ports: ${diagnostics.ports.status}`);
    console.log(`Service: ${diagnostics.service.running ? 'Running' : 'Not Running'}`);
    console.log(`Processes: ${diagnostics.processes.running ? 'Found' : 'Not Found'}`);
    console.log(`Installation: ${diagnostics.installation.exists ? 'Found' : 'Not Found'}`);
    
    // Step 2: Apply fixes
    const fixes = await this.fixFastPanel();
    
    console.log('\n🛠️  Applied Fixes:');
    console.log('==================');
    fixes.forEach(fix => console.log(`  ${fix}`));
    
    // Step 3: Create proxy if needed
    console.log('\n🔄 Setting up FastPanel proxy...');
    await this.createFastPanelProxy();
    
    // Step 4: Final verification
    console.log('\n🔍 Final Verification:');
    console.log('======================');
    
    const final_check = await this.exec('ss -tlnp | grep -E ":(7777|8888)"');
    if (final_check.code === 0) {
      console.log('✅ FastPanel ports are listening');
    } else {
      console.log('❌ FastPanel ports still not accessible');
    }
    
    const web_check = await this.exec('curl -I http://localhost:7777 2>/dev/null');
    if (web_check.code === 0) {
      console.log('✅ FastPanel web interface is responding');
    } else {
      console.log('❌ FastPanel web interface not responding');
    }
    
    console.log('\n🎯 FastPanel Fix Complete!');
    console.log('If FastPanel is still not accessible, it may need manual installation or configuration.');
  }
}

// Run the FastPanel fixer
const fixer = new FastPanelFixer();
fixer.comprehensiveFastPanelFix().catch(console.error);
