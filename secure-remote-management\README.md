# 🔒 Secure Remote Server Management Tool

A comprehensive, secure remote server management tool designed specifically for AI coding assistants (like <PERSON><PERSON> Code, Composer Agent) to safely access and manage backend VPS servers for automated troubleshooting and fixes.

## 🎯 Overview

This tool enables AI assistants to securely perform server management tasks on your backend VPS (***********) while maintaining strict security controls, comprehensive audit trails, and emergency recovery capabilities.

## ✨ Key Features

### 🔐 Security First
- **SSH Key-Based Authentication**: Passwordless access with RSA 4096-bit keys
- **Command Whitelisting**: Only pre-approved command patterns allowed
- **Audit Logging**: Complete trail of all operations and changes
- **Sandboxed Execution**: Commands run in controlled environment
- **Automatic Backups**: Configuration backups before changes

### 🤖 AI Assistant Integration
- **RESTful API**: Standard HTTP API for AI assistant interaction
- **WebSocket Support**: Real-time communication for long operations
- **Structured Commands**: JSON-based command specification
- **Session Management**: Secure token-based authentication

### 🔍 Comprehensive Monitoring
- **Real-Time Diagnostics**: Service status, resource usage, connectivity
- **Health Scoring**: Overall system health assessment
- **Alert System**: Automated alerts for critical conditions
- **Performance Metrics**: Historical data and trend analysis

### 🛠️ Server Management
- **Service Control**: PM2, Nginx, MySQL, FastPanel management
- **Configuration Management**: Safe editing with backup/restore
- **Database Operations**: Secure MySQL operations via socket
- **Emergency Recovery**: Automated recovery procedures

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- SSH client (OpenSSH)
- Access to target server (***********)

### Installation

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd secure-remote-management
   chmod +x install.sh
   ./install.sh
   ```

2. **Configure Settings**
   ```bash
   # Edit configuration
   nano ~/.config/remote-server-manager/server-config.json
   
   # Set up environment variables
   cp ~/.config/remote-server-manager/.env.template ~/.config/remote-server-manager/.env
   nano ~/.config/remote-server-manager/.env
   ```

3. **Start Using**
   ```bash
   # CLI mode
   remote-server-manager start
   
   # API mode for AI assistants
   remote-server-manager start --mode api
   
   # Quick diagnostics
   remote-server-manager diagnostics
   ```

## 📖 Usage Guide

### CLI Mode

Interactive command-line interface for manual server management:

```bash
# Start interactive CLI
rsm start

# Available options:
# 🔍 Run Server Diagnostics
# ⚡ Execute Command
# 🔄 Restart Service
# 📊 Start Monitoring
# 🔧 Manage Configuration
# 💾 Database Operations
# 🚀 Start API Server
```

### API Mode

RESTful API server for AI assistant integration:

```bash
# Start API server
rsm start --mode api

# Server runs on http://localhost:3000
# WebSocket available for real-time updates
```

### Emergency Mode

Emergency recovery for critical situations:

```bash
# Emergency recovery mode
rsm emergency

# Available recovery actions:
# 🔄 Restart all services
# 🔍 Check system status
# 📋 View recent logs
# 🔙 Rollback last operation
```

## 🔌 API Integration for AI Assistants

### Authentication

```javascript
// Login to get access token
POST /auth/login
{
  "apiKey": "your-api-key",
  "clientId": "augment-code-assistant"
}

// Response
{
  "success": true,
  "token": "jwt-token",
  "sessionId": "session-id",
  "expiresAt": "2024-01-01T12:00:00Z"
}
```

### Example: Augment Code Integration

```javascript
// Example integration for Augment Code
class AugmentServerManager {
  constructor(apiUrl = 'http://localhost:3000') {
    this.apiUrl = apiUrl;
    this.token = null;
  }

  async authenticate(apiKey) {
    const response = await fetch(`${this.apiUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        apiKey,
        clientId: 'augment-code-assistant'
      })
    });

    const data = await response.json();
    this.token = data.token;
    return data;
  }

  async executeCommand(command, options = {}) {
    const response = await fetch(`${this.apiUrl}/server/command`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body: JSON.stringify({ command, options })
    });

    return await response.json();
  }

  async getDiagnostics() {
    const response = await fetch(`${this.apiUrl}/server/diagnostics`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });

    return await response.json();
  }
}
```

### Server Management

```javascript
// Run diagnostics
GET /server/diagnostics
Authorization: Bearer <token>

// Execute command
POST /server/command
Authorization: Bearer <token>
{
  "command": "systemctl status nginx",
  "options": { "dryRun": false }
}

// Restart service
POST /services/nginx/restart
Authorization: Bearer <token>
```

### Real-Time Updates

```javascript
// WebSocket connection
const socket = io('http://localhost:3000');

// Authenticate
socket.emit('authenticate', 'jwt-token');

// Subscribe to monitoring updates
socket.emit('subscribe-monitoring');

// Listen for events
socket.on('command-executed', (data) => {
  console.log('Command executed:', data);
});
```

## 🛡️ Security Features

### Command Validation
- **Whitelist-Based**: Only approved command patterns allowed
- **Dangerous Pattern Detection**: Blocks potentially harmful commands
- **Context-Aware**: Permissions based on current system state

### Audit Trail
- **Complete Logging**: Every command, file change, and access attempt
- **Tamper-Proof**: Secure log storage with integrity verification
- **Compliance Ready**: Meets enterprise audit requirements

### Access Control
- **Token-Based**: JWT tokens with configurable expiration
- **Session Management**: Active session tracking and cleanup
- **Rate Limiting**: Protection against abuse and brute force

## 📊 Monitoring & Diagnostics

### Health Checks
- **Service Status**: PM2, MySQL, Nginx, FastPanel monitoring
- **Resource Usage**: CPU, memory, disk space tracking
- **Network Connectivity**: Port scanning and connectivity tests
- **Security Status**: Firewall, SSH configuration, failed logins

### Performance Metrics
- **Response Times**: Command execution timing
- **Success Rates**: Operation success/failure statistics
- **Resource Trends**: Historical resource usage patterns
- **Error Analysis**: Automated error pattern recognition

### Alert System
- **Threshold-Based**: Configurable alert thresholds
- **Real-Time**: Immediate notification of critical conditions
- **Multi-Channel**: Support for various notification methods

## 🔧 Configuration

### Server Configuration
```json
{
  "serverIP": "***********",
  "serverUser": "root",
  "maxExecutionTime": 300000,
  "maxOutputSize": 10485760,
  "auditLogPath": "./logs/command-audit.log",
  "backupDir": "./backups",
  "alertThresholds": {
    "cpuUsage": 80,
    "memoryUsage": 85,
    "diskUsage": 90,
    "responseTime": 5000
  }
}
```

### API Configuration
```json
{
  "apiConfig": {
    "port": 3000,
    "enableWebSocket": true,
    "sessionTimeout": 3600000,
    "maxConcurrentOperations": 5
  }
}
```

### Environment Variables
```bash
# API Security
JWT_SECRET=your-very-long-random-secret-key
VALID_API_KEYS=key1,key2,key3

# Server Access
SERVER_IP=***********
SERVER_USER=root

# Database (if needed)
DB_PASSWORD=your-database-password
```

## 🚨 Emergency Procedures

### Service Recovery
```bash
# Restart all critical services
rsm emergency
# Select: 🔄 Restart all services

# Manual service restart
rsm start
# Select: 🔄 Restart Service
```

### Configuration Rollback
```bash
# Automatic rollback (if backup exists)
curl -X POST http://localhost:3000/config/restore/backup-id \
  -H "Authorization: Bearer <token>"

# Manual rollback
# 1. Check /tmp/ for recent backups
# 2. Restore configuration files
# 3. Restart affected services
```

### Emergency SSH Access
```bash
# Direct SSH access (bypass tool)
ssh root@***********

# Check service status
systemctl status nginx mysql
pm2 status

# View logs
journalctl -f
tail -f /var/log/nginx/error.log
```

## 📁 Directory Structure

```
~/.remote-server-manager/          # Installation directory
├── remote-server-manager.js       # Main entry point
├── ssh-auth-manager.js            # SSH authentication
├── command-executor.js            # Command execution
├── server-diagnostics.js          # Health monitoring
├── ai-integration-api.js          # API server
└── package.json                   # Dependencies

~/.config/remote-server-manager/   # Configuration
├── server-config.json             # Main configuration
└── .env                          # Environment variables

~/.local/share/remote-server-manager/
├── logs/                         # Audit logs
└── backups/                      # Configuration backups
```

## 🔍 Troubleshooting

### Common Issues

**SSH Connection Failed**
```bash
# Check SSH key setup
ssh-keygen -l -f ~/.ssh/id_rsa.pub

# Test manual connection
ssh root@***********

# Regenerate keys if needed
rsm start
# Follow SSH setup prompts
```

**API Server Won't Start**
```bash
# Check port availability
netstat -tlnp | grep :3000

# Check configuration
cat ~/.config/remote-server-manager/server-config.json

# Check logs
tail -f ~/.local/share/remote-server-manager/logs/command-audit.log
```

**Command Execution Failed**
```bash
# Check command whitelist
# Commands must match approved patterns

# Use dry-run mode for testing
rsm start
# Select: ⚡ Execute Command
# Enable dry-run mode
```

### Debug Mode
```bash
# Enable verbose logging
DEBUG=* rsm start --mode api

# Check system requirements
node --version  # Should be 16+
ssh -V         # Should be OpenSSH
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs via GitHub issues
- **Emergency**: Use emergency recovery mode for critical situations

---

**⚠️ Security Notice**: This tool provides powerful server access capabilities. Always:
- Use strong API keys and JWT secrets
- Regularly rotate SSH keys
- Monitor audit logs
- Keep the tool updated
- Follow principle of least privilege
