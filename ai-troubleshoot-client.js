#!/usr/bin/env node

/**
 * 🤖 AI Troubleshooting Client
 * Direct server access for automated issue resolution
 */

const https = require('https');
const http = require('http');

class AITroubleshootClient {
  constructor(managementServerUrl = 'http://localhost:3000') {
    this.baseUrl = managementServerUrl;
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const url = new URL(endpoint, this.baseUrl);
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        const jsonData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(jsonData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          try {
            const parsed = JSON.parse(responseData);
            resolve(parsed);
          } catch (error) {
            resolve({ raw: responseData });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      
      req.end();
    });
  }

  async checkConnection() {
    try {
      console.log('🔌 Checking connection to management server...');
      const health = await this.makeRequest('/health');
      
      if (health.status === 'healthy' && health.connected) {
        console.log('✅ Connected to server successfully');
        return true;
      } else {
        console.log('❌ Server connection failed');
        return false;
      }
    } catch (error) {
      console.log('❌ Management server not accessible:', error.message);
      return false;
    }
  }

  async runDiagnostics() {
    try {
      console.log('🔍 Running comprehensive server diagnostics...');
      const diagnostics = await this.makeRequest('/diagnostics');
      
      console.log('\n📊 Diagnostics Results:');
      console.log('========================');
      
      // System status
      if (diagnostics.checks.system) {
        console.log('\n🖥️  System Status:');
        if (diagnostics.checks.system.uptime) {
          console.log('  Uptime:', diagnostics.checks.system.uptime.trim());
        }
        if (diagnostics.checks.system.memory) {
          const memLines = diagnostics.checks.system.memory.split('\n');
          const memLine = memLines.find(line => line.startsWith('Mem:'));
          if (memLine) {
            console.log('  Memory:', memLine.trim());
          }
        }
      }
      
      // Service status
      if (diagnostics.checks.services) {
        console.log('\n🔧 Service Status:');
        
        // Check Nginx
        if (diagnostics.checks.services.nginx) {
          const nginxRunning = diagnostics.checks.services.nginx.includes('active (running)');
          console.log(`  Nginx: ${nginxRunning ? '✅ Running' : '❌ Not Running'}`);
        }
        
        // Check MySQL
        if (diagnostics.checks.services.mysql) {
          const mysqlRunning = diagnostics.checks.services.mysql.includes('active (running)');
          console.log(`  MySQL: ${mysqlRunning ? '✅ Running' : '❌ Not Running'}`);
        }
        
        // Check PM2
        if (diagnostics.checks.services.pm2) {
          const pm2HasProcesses = diagnostics.checks.services.pm2.includes('online');
          console.log(`  PM2 Processes: ${pm2HasProcesses ? '✅ Running' : '❌ No processes'}`);
        }
      }
      
      // Network status
      if (diagnostics.checks.network) {
        console.log('\n🌐 Network Status:');
        if (diagnostics.checks.network.nginx_config) {
          const nginxOk = diagnostics.checks.network.nginx_config.includes('syntax is ok');
          console.log(`  Nginx Config: ${nginxOk ? '✅ Valid' : '❌ Invalid'}`);
        }
      }
      
      return diagnostics;
    } catch (error) {
      console.error('❌ Diagnostics failed:', error.message);
      return null;
    }
  }

  async executeCommand(command) {
    try {
      console.log(`🔄 Executing: ${command}`);
      const result = await this.makeRequest('/execute', 'POST', { command });
      
      if (result.code === 0) {
        console.log('✅ Command successful');
        if (result.stdout) {
          console.log('📤 Output:', result.stdout);
        }
      } else {
        console.log('❌ Command failed');
        if (result.stderr) {
          console.log('📥 Error:', result.stderr);
        }
      }
      
      return result;
    } catch (error) {
      console.error('❌ Command execution failed:', error.message);
      return null;
    }
  }

  async runAutomatedFixes() {
    try {
      console.log('🔧 Running automated fixes...');
      const result = await this.makeRequest('/fix', 'POST');
      
      console.log('\n🛠️  Fix Results:');
      console.log('================');
      
      if (result.fixes) {
        result.fixes.forEach(fix => {
          console.log(`  ${fix}`);
        });
      }
      
      return result;
    } catch (error) {
      console.error('❌ Automated fixes failed:', error.message);
      return null;
    }
  }

  async comprehensiveTroubleshoot() {
    console.log('🚀 Starting comprehensive troubleshooting...\n');
    
    // Step 1: Check connection
    const connected = await this.checkConnection();
    if (!connected) {
      console.log('❌ Cannot proceed - management server not accessible');
      return false;
    }
    
    // Step 2: Run diagnostics
    const diagnostics = await this.runDiagnostics();
    if (!diagnostics) {
      console.log('❌ Cannot proceed - diagnostics failed');
      return false;
    }
    
    // Step 3: Identify issues
    const issues = this.identifyIssues(diagnostics);
    
    if (issues.length === 0) {
      console.log('\n🎉 No issues detected! Server appears to be healthy.');
      return true;
    }
    
    console.log('\n🔍 Issues Detected:');
    issues.forEach(issue => {
      console.log(`  ❌ ${issue}`);
    });
    
    // Step 4: Run automated fixes
    console.log('\n🔧 Attempting automated fixes...');
    await this.runAutomatedFixes();
    
    // Step 5: Re-run diagnostics to verify fixes
    console.log('\n🔍 Verifying fixes...');
    const postFixDiagnostics = await this.runDiagnostics();
    
    const remainingIssues = this.identifyIssues(postFixDiagnostics);
    
    if (remainingIssues.length === 0) {
      console.log('\n🎉 All issues resolved successfully!');
      return true;
    } else {
      console.log('\n⚠️  Some issues remain:');
      remainingIssues.forEach(issue => {
        console.log(`  ❌ ${issue}`);
      });
      return false;
    }
  }

  identifyIssues(diagnostics) {
    const issues = [];
    
    if (!diagnostics || !diagnostics.checks) {
      issues.push('Unable to retrieve diagnostics');
      return issues;
    }
    
    // Check services
    if (diagnostics.checks.services) {
      if (diagnostics.checks.services.nginx && !diagnostics.checks.services.nginx.includes('active (running)')) {
        issues.push('Nginx service is not running');
      }
      
      if (diagnostics.checks.services.mysql && !diagnostics.checks.services.mysql.includes('active (running)')) {
        issues.push('MySQL service is not running');
      }
      
      if (diagnostics.checks.services.pm2 && !diagnostics.checks.services.pm2.includes('online')) {
        issues.push('No PM2 processes are running');
      }
    }
    
    // Check network/config
    if (diagnostics.checks.network) {
      if (diagnostics.checks.network.nginx_config && !diagnostics.checks.network.nginx_config.includes('syntax is ok')) {
        issues.push('Nginx configuration has syntax errors');
      }
    }
    
    // Check application
    if (diagnostics.checks.application) {
      if (diagnostics.checks.application.env_file && diagnostics.checks.application.env_file.includes('No such file')) {
        issues.push('Environment file (.env) is missing');
      }
    }
    
    return issues;
  }

  async specificFixes() {
    console.log('🎯 Running specific StreamDB fixes...\n');
    
    // Fix 1: Ensure application files are present
    console.log('📁 Checking application files...');
    await this.executeCommand('ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/');
    
    // Fix 2: Check and fix environment file
    console.log('\n🔧 Checking environment configuration...');
    const envCheck = await this.executeCommand('ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env');
    
    if (envCheck && envCheck.stderr && envCheck.stderr.includes('No such file')) {
      console.log('⚠️  Environment file missing, creating basic .env...');
      await this.executeCommand(`cat > /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env << 'EOF'
PORT=3001
NODE_ENV=production
DB_HOST=localhost
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_SOCKET=/var/run/mysqld/mysqld.sock
EOF`);
    }
    
    // Fix 3: Install dependencies if needed
    console.log('\n📦 Checking Node.js dependencies...');
    await this.executeCommand('cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server && npm install --production');
    
    // Fix 4: Set proper permissions
    console.log('\n🔒 Setting proper permissions...');
    await this.executeCommand('chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online/');
    await this.executeCommand('chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online/');
    await this.executeCommand('chmod 600 /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env');
    
    // Fix 5: Start/restart services
    console.log('\n🔄 Restarting services...');
    await this.executeCommand('systemctl restart nginx');
    await this.executeCommand('systemctl restart mysql');
    
    // Fix 6: Start PM2 processes
    console.log('\n🚀 Starting application processes...');
    await this.executeCommand('cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server && pm2 start index.js --name streamdb-api');
    await this.executeCommand('pm2 save');
    
    // Fix 7: Test the application
    console.log('\n🧪 Testing application...');
    await this.executeCommand('curl -I http://localhost:3001/health || echo "Application not responding"');
    
    console.log('\n✅ Specific fixes completed');
  }
}

// CLI usage
if (require.main === module) {
  const client = new AITroubleshootClient();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'check':
      client.checkConnection();
      break;
    case 'diagnose':
      client.runDiagnostics();
      break;
    case 'fix':
      client.runAutomatedFixes();
      break;
    case 'troubleshoot':
      client.comprehensiveTroubleshoot();
      break;
    case 'specific':
      client.specificFixes();
      break;
    default:
      console.log('🤖 AI Troubleshooting Client');
      console.log('Usage:');
      console.log('  node ai-troubleshoot-client.js check        - Check connection');
      console.log('  node ai-troubleshoot-client.js diagnose     - Run diagnostics');
      console.log('  node ai-troubleshoot-client.js fix          - Run automated fixes');
      console.log('  node ai-troubleshoot-client.js troubleshoot - Full troubleshooting');
      console.log('  node ai-troubleshoot-client.js specific     - StreamDB specific fixes');
  }
}

module.exports = AITroubleshootClient;
