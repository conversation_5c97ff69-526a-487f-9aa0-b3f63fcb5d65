#!/usr/bin/env node

/**
 * 🤖 AI Assistant Integration API
 * RESTful API for AI coding assistants to interact with server management tools
 * 
 * Features:
 * - Structured command interface for AI assistants
 * - Session management and authentication
 * - Real-time operation monitoring
 * - Comprehensive error handling and logging
 * - WebSocket support for long-running operations
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');

const SSHAuthManager = require('./ssh-auth-manager');
const SecureCommandExecutor = require('./command-executor');
const ServerDiagnostics = require('./server-diagnostics');

class AIIntegrationAPI {
  constructor(config = {}) {
    this.config = {
      port: config.port || 3000,
      jwtSecret: config.jwtSecret || crypto.randomBytes(64).toString('hex'),
      sessionTimeout: config.sessionTimeout || 3600000, // 1 hour
      maxConcurrentOperations: config.maxConcurrentOperations || 5,
      enableWebSocket: config.enableWebSocket !== false,
      ...config
    };

    this.app = express();
    this.server = createServer(this.app);
    this.io = this.config.enableWebSocket ? new Server(this.server, {
      cors: { origin: "*", methods: ["GET", "POST"] }
    }) : null;

    this.sshManager = new SSHAuthManager(config);
    this.commandExecutor = new SecureCommandExecutor(config);
    this.diagnostics = new ServerDiagnostics(config);

    this.activeSessions = new Map();
    this.activeOperations = new Map();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });

    // Authentication
    this.app.post('/auth/login', this.handleLogin.bind(this));
    this.app.post('/auth/refresh', this.authenticateToken.bind(this), this.handleRefresh.bind(this));
    this.app.post('/auth/logout', this.authenticateToken.bind(this), this.handleLogout.bind(this));

    // Server management endpoints
    this.app.get('/server/status', this.authenticateToken.bind(this), this.getServerStatus.bind(this));
    this.app.get('/server/diagnostics', this.authenticateToken.bind(this), this.getServerDiagnostics.bind(this));
    this.app.post('/server/command', this.authenticateToken.bind(this), this.executeCommand.bind(this));
    this.app.get('/server/operations', this.authenticateToken.bind(this), this.getActiveOperations.bind(this));
    this.app.delete('/server/operations/:id', this.authenticateToken.bind(this), this.killOperation.bind(this));

    // Service management
    this.app.post('/services/:service/restart', this.authenticateToken.bind(this), this.restartService.bind(this));
    this.app.post('/services/:service/reload', this.authenticateToken.bind(this), this.reloadService.bind(this));
    this.app.get('/services/:service/status', this.authenticateToken.bind(this), this.getServiceStatus.bind(this));
    this.app.get('/services/:service/logs', this.authenticateToken.bind(this), this.getServiceLogs.bind(this));

    // Configuration management
    this.app.get('/config/files', this.authenticateToken.bind(this), this.listConfigFiles.bind(this));
    this.app.get('/config/files/*', this.authenticateToken.bind(this), this.getConfigFile.bind(this));
    this.app.put('/config/files/*', this.authenticateToken.bind(this), this.updateConfigFile.bind(this));
    this.app.post('/config/backup', this.authenticateToken.bind(this), this.createConfigBackup.bind(this));
    this.app.post('/config/restore/:backupId', this.authenticateToken.bind(this), this.restoreConfigBackup.bind(this));

    // Database management
    this.app.get('/database/status', this.authenticateToken.bind(this), this.getDatabaseStatus.bind(this));
    this.app.post('/database/query', this.authenticateToken.bind(this), this.executeDatabaseQuery.bind(this));
    this.app.post('/database/backup', this.authenticateToken.bind(this), this.createDatabaseBackup.bind(this));
    this.app.post('/database/restore/:backupId', this.authenticateToken.bind(this), this.restoreDatabaseBackup.bind(this));

    // Monitoring and alerts
    this.app.get('/monitoring/metrics', this.authenticateToken.bind(this), this.getMonitoringMetrics.bind(this));
    this.app.post('/monitoring/start', this.authenticateToken.bind(this), this.startMonitoring.bind(this));
    this.app.post('/monitoring/stop', this.authenticateToken.bind(this), this.stopMonitoring.bind(this));

    // Error handling
    this.app.use(this.errorHandler.bind(this));
  }

  /**
   * Setup WebSocket for real-time communication
   */
  setupWebSocket() {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      console.log('WebSocket client connected:', socket.id);

      socket.on('authenticate', async (token) => {
        try {
          const decoded = jwt.verify(token, this.config.jwtSecret);
          socket.userId = decoded.userId;
          socket.sessionId = decoded.sessionId;
          socket.emit('authenticated', { success: true });
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Invalid token' });
        }
      });

      socket.on('subscribe-monitoring', () => {
        if (socket.userId) {
          socket.join('monitoring');
          console.log('Client subscribed to monitoring updates');
        }
      });

      socket.on('disconnect', () => {
        console.log('WebSocket client disconnected:', socket.id);
      });
    });
  }

  /**
   * Authentication middleware
   */
  authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, this.config.jwtSecret, (err, decoded) => {
      if (err) {
        return res.status(403).json({ error: 'Invalid or expired token' });
      }

      // Check if session is still active
      const session = this.activeSessions.get(decoded.sessionId);
      if (!session || session.expiresAt < Date.now()) {
        return res.status(403).json({ error: 'Session expired' });
      }

      req.user = decoded;
      req.sessionId = decoded.sessionId;
      next();
    });
  }

  /**
   * Handle login
   */
  async handleLogin(req, res) {
    try {
      const { apiKey, clientId } = req.body;

      // Validate API key (implement your own validation logic)
      if (!this.validateAPIKey(apiKey)) {
        return res.status(401).json({ error: 'Invalid API key' });
      }

      // Create session
      const sessionId = crypto.randomUUID();
      const userId = clientId || 'ai-assistant';
      const expiresAt = Date.now() + this.config.sessionTimeout;

      this.activeSessions.set(sessionId, {
        userId,
        clientId,
        createdAt: Date.now(),
        expiresAt,
        lastActivity: Date.now()
      });

      // Generate JWT token
      const token = jwt.sign(
        { userId, sessionId, clientId },
        this.config.jwtSecret,
        { expiresIn: '1h' }
      );

      res.json({
        success: true,
        token,
        sessionId,
        expiresAt: new Date(expiresAt).toISOString()
      });

    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Handle token refresh
   */
  async handleRefresh(req, res) {
    try {
      const session = this.activeSessions.get(req.sessionId);
      if (!session) {
        return res.status(403).json({ error: 'Session not found' });
      }

      // Extend session
      session.expiresAt = Date.now() + this.config.sessionTimeout;
      session.lastActivity = Date.now();

      // Generate new token
      const token = jwt.sign(
        { userId: session.userId, sessionId: req.sessionId, clientId: session.clientId },
        this.config.jwtSecret,
        { expiresIn: '1h' }
      );

      res.json({
        success: true,
        token,
        expiresAt: new Date(session.expiresAt).toISOString()
      });

    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Handle logout
   */
  async handleLogout(req, res) {
    try {
      this.activeSessions.delete(req.sessionId);
      res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get server status
   */
  async getServerStatus(req, res) {
    try {
      const status = await this.sshManager.getConnectionStatus();
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get server diagnostics
   */
  async getServerDiagnostics(req, res) {
    try {
      const diagnostics = await this.diagnostics.runDiagnostics();
      res.json(diagnostics);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Execute command
   */
  async executeCommand(req, res) {
    try {
      const { command, options = {} } = req.body;

      if (!command) {
        return res.status(400).json({ error: 'Command is required' });
      }

      // Check concurrent operations limit
      if (this.activeOperations.size >= this.config.maxConcurrentOperations) {
        return res.status(429).json({ error: 'Too many concurrent operations' });
      }

      const result = await this.commandExecutor.executeCommand(command, options);
      
      // Store operation for tracking
      this.activeOperations.set(result.operationId, {
        command,
        result,
        userId: req.user.userId,
        timestamp: new Date().toISOString()
      });

      // Emit to WebSocket subscribers
      if (this.io) {
        this.io.to('monitoring').emit('command-executed', {
          operationId: result.operationId,
          command,
          success: result.success,
          timestamp: new Date().toISOString()
        });
      }

      res.json(result);

    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get active operations
   */
  async getActiveOperations(req, res) {
    try {
      const operations = Array.from(this.activeOperations.entries()).map(([id, op]) => ({
        operationId: id,
        ...op
      }));

      res.json({ operations });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Kill operation
   */
  async killOperation(req, res) {
    try {
      const { id } = req.params;
      await this.commandExecutor.killOperation(id);
      this.activeOperations.delete(id);
      
      res.json({ success: true, message: 'Operation killed' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Restart service
   */
  async restartService(req, res) {
    try {
      const { service } = req.params;
      const result = await this.commandExecutor.executeCommand(`systemctl restart ${service}`);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Reload service
   */
  async reloadService(req, res) {
    try {
      const { service } = req.params;
      const result = await this.commandExecutor.executeCommand(`systemctl reload ${service}`);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get service status
   */
  async getServiceStatus(req, res) {
    try {
      const { service } = req.params;
      const result = await this.commandExecutor.executeCommand(`systemctl status ${service}`);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get service logs
   */
  async getServiceLogs(req, res) {
    try {
      const { service } = req.params;
      const lines = req.query.lines || 50;
      const result = await this.commandExecutor.executeCommand(`journalctl -u ${service} -n ${lines}`);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * List configuration files
   */
  async listConfigFiles(req, res) {
    try {
      const configDirs = [
        '/etc/nginx/fastpanel2-sites/',
        '/var/www/streamdb_onl_usr/data/www/streamdb.online/server/',
        '/etc/mysql/'
      ];

      const files = [];
      for (const dir of configDirs) {
        try {
          const result = await this.commandExecutor.executeCommand(`find ${dir} -name "*.conf" -o -name "*.env" -o -name "*.cnf"`);
          if (result.stdout) {
            files.push(...result.stdout.split('\n').filter(f => f.trim()));
          }
        } catch (error) {
          // Directory might not exist, continue
        }
      }

      res.json({ files });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get configuration file content
   */
  async getConfigFile(req, res) {
    try {
      const filePath = req.params[0];
      const result = await this.commandExecutor.executeCommand(`cat ${filePath}`);
      res.json({ content: result.stdout, path: filePath });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update configuration file
   */
  async updateConfigFile(req, res) {
    try {
      const filePath = req.params[0];
      const { content } = req.body;

      // Create backup first
      const backupPath = `${filePath}.backup.${Date.now()}`;
      await this.commandExecutor.executeCommand(`cp ${filePath} ${backupPath}`);

      // Write new content (this would need a more sophisticated approach in production)
      const tempFile = `/tmp/config_update_${Date.now()}`;
      await this.commandExecutor.executeCommand(`echo '${content}' > ${tempFile}`);
      await this.commandExecutor.executeCommand(`mv ${tempFile} ${filePath}`);

      res.json({ success: true, backupPath });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create configuration backup
   */
  async createConfigBackup(req, res) {
    try {
      const timestamp = Date.now();
      const backupDir = `/tmp/config_backup_${timestamp}`;

      await this.commandExecutor.executeCommand(`mkdir -p ${backupDir}`);
      await this.commandExecutor.executeCommand(`cp -r /etc/nginx/fastpanel2-sites/ ${backupDir}/nginx/`);
      await this.commandExecutor.executeCommand(`cp /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env ${backupDir}/`);

      res.json({ success: true, backupId: `config_backup_${timestamp}`, backupDir });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Restore configuration backup
   */
  async restoreConfigBackup(req, res) {
    try {
      const { backupId } = req.params;
      const backupDir = `/tmp/${backupId}`;

      // Restore files
      await this.commandExecutor.executeCommand(`cp -r ${backupDir}/nginx/* /etc/nginx/fastpanel2-sites/`);
      await this.commandExecutor.executeCommand(`cp ${backupDir}/.env /var/www/streamdb_onl_usr/data/www/streamdb.online/server/`);

      // Reload nginx
      await this.commandExecutor.executeCommand('systemctl reload nginx');

      res.json({ success: true, message: 'Configuration restored successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get database status
   */
  async getDatabaseStatus(req, res) {
    try {
      const result = await this.commandExecutor.executeCommand('mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -e "SHOW DATABASES;"');
      res.json({ connected: true, databases: result.stdout });
    } catch (error) {
      res.status(500).json({ error: error.message, connected: false });
    }
  }

  /**
   * Execute database query
   */
  async executeDatabaseQuery(req, res) {
    try {
      const { query, database = 'streamdb_database' } = req.body;
      const result = await this.commandExecutor.executeCommand(
        `mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -e "${query}" ${database}`
      );
      res.json({ success: true, result: result.stdout });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create database backup
   */
  async createDatabaseBackup(req, res) {
    try {
      const timestamp = Date.now();
      const backupFile = `/tmp/streamdb_backup_${timestamp}.sql`;

      await this.commandExecutor.executeCommand(
        `mysqldump --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb streamdb_database > ${backupFile}`
      );

      res.json({ success: true, backupId: `streamdb_backup_${timestamp}`, backupFile });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Restore database backup
   */
  async restoreDatabaseBackup(req, res) {
    try {
      const { backupId } = req.params;
      const backupFile = `/tmp/${backupId}.sql`;

      await this.commandExecutor.executeCommand(
        `mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb streamdb_database < ${backupFile}`
      );

      res.json({ success: true, message: 'Database restored successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get monitoring metrics
   */
  async getMonitoringMetrics(req, res) {
    try {
      const history = this.diagnostics.getHealthHistory();
      res.json({ metrics: history });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Start monitoring
   */
  async startMonitoring(req, res) {
    try {
      this.diagnostics.startMonitoring();
      res.json({ success: true, message: 'Monitoring started' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Stop monitoring
   */
  async stopMonitoring(req, res) {
    try {
      this.diagnostics.stopMonitoring();
      res.json({ success: true, message: 'Monitoring stopped' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Validate API key
   */
  validateAPIKey(apiKey) {
    // Implement your API key validation logic here
    // This is a simple example - use proper validation in production
    const validKeys = process.env.VALID_API_KEYS?.split(',') || ['demo-key-123'];
    return validKeys.includes(apiKey);
  }

  /**
   * Error handler
   */
  errorHandler(error, req, res, next) {
    console.error('API Error:', error);
    
    if (res.headersSent) {
      return next(error);
    }

    res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Start the API server
   */
  async start() {
    try {
      // Initialize SSH connection
      await this.sshManager.initialize();
      
      // Start server
      this.server.listen(this.config.port, () => {
        console.log(`🚀 AI Integration API server running on port ${this.config.port}`);
        console.log(`📡 WebSocket ${this.config.enableWebSocket ? 'enabled' : 'disabled'}`);
        console.log(`🔐 JWT Secret: ${this.config.jwtSecret.substring(0, 8)}...`);
      });

      // Cleanup expired sessions periodically
      setInterval(() => {
        const now = Date.now();
        for (const [sessionId, session] of this.activeSessions) {
          if (session.expiresAt < now) {
            this.activeSessions.delete(sessionId);
          }
        }
      }, 60000); // Check every minute

    } catch (error) {
      console.error('Failed to start API server:', error.message);
      process.exit(1);
    }
  }

  /**
   * Stop the API server
   */
  async stop() {
    return new Promise((resolve) => {
      this.server.close(() => {
        console.log('🛑 AI Integration API server stopped');
        resolve();
      });
    });
  }
}

module.exports = AIIntegrationAPI;

// CLI usage
if (require.main === module) {
  const api = new AIIntegrationAPI({
    port: process.env.PORT || 3000,
    jwtSecret: process.env.JWT_SECRET
  });

  api.start().catch(console.error);

  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down gracefully...');
    await api.stop();
    process.exit(0);
  });
}
