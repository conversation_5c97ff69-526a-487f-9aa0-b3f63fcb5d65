#!/bin/bash

# 🔒 Secure Remote Server Management Tool - Installation Script
# Automated installation and setup for AI-assisted server management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TOOL_NAME="Secure Remote Server Manager"
INSTALL_DIR="$HOME/.remote-server-manager"
CONFIG_DIR="$HOME/.config/remote-server-manager"
LOG_DIR="$HOME/.local/share/remote-server-manager/logs"
BACKUP_DIR="$HOME/.local/share/remote-server-manager/backups"

# Server configuration
DEFAULT_SERVER_IP="***********"
DEFAULT_SERVER_USER="root"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 16+ first."
        print_status "Visit: https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 16 ]; then
        print_error "Node.js version 16+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check SSH
    if ! command_exists ssh; then
        print_error "SSH client is not installed. Please install OpenSSH client first."
        exit 1
    fi
    
    # Check ssh-keygen
    if ! command_exists ssh-keygen; then
        print_error "ssh-keygen is not installed. Please install OpenSSH client first."
        exit 1
    fi
    
    print_success "All system requirements met"
}

# Function to create directories
create_directories() {
    print_status "Creating directories..."
    
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # Set proper permissions
    chmod 700 "$INSTALL_DIR"
    chmod 700 "$CONFIG_DIR"
    chmod 755 "$LOG_DIR"
    chmod 700 "$BACKUP_DIR"
    
    print_success "Directories created successfully"
}

# Function to install the tool
install_tool() {
    print_status "Installing $TOOL_NAME..."
    
    # Copy files to installation directory
    cp -r ./* "$INSTALL_DIR/"
    
    # Install dependencies
    cd "$INSTALL_DIR"
    print_status "Installing Node.js dependencies..."
    npm install --production
    
    # Make scripts executable
    chmod +x "$INSTALL_DIR/remote-server-manager.js"
    chmod +x "$INSTALL_DIR/scripts/"*.js 2>/dev/null || true
    
    print_success "Tool installed successfully"
}

# Function to create symlinks
create_symlinks() {
    print_status "Creating command line shortcuts..."
    
    # Create symlink for global access
    local bin_dir="$HOME/.local/bin"
    mkdir -p "$bin_dir"
    
    ln -sf "$INSTALL_DIR/remote-server-manager.js" "$bin_dir/remote-server-manager"
    ln -sf "$INSTALL_DIR/remote-server-manager.js" "$bin_dir/rsm"
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$bin_dir:"* ]]; then
        echo "export PATH=\"\$PATH:$bin_dir\"" >> "$HOME/.bashrc"
        echo "export PATH=\"\$PATH:$bin_dir\"" >> "$HOME/.zshrc" 2>/dev/null || true
        print_warning "Added $bin_dir to PATH. Please restart your shell or run: source ~/.bashrc"
    fi
    
    print_success "Command line shortcuts created"
}

# Function to configure the tool
configure_tool() {
    print_status "Configuring $TOOL_NAME..."
    
    # Create default configuration
    cat > "$CONFIG_DIR/server-config.json" << EOF
{
  "serverIP": "$DEFAULT_SERVER_IP",
  "serverUser": "$DEFAULT_SERVER_USER",
  "maxExecutionTime": 300000,
  "maxOutputSize": 10485760,
  "auditLogPath": "$LOG_DIR/command-audit.log",
  "backupDir": "$BACKUP_DIR",
  "dryRun": false,
  "alertThresholds": {
    "cpuUsage": 80,
    "memoryUsage": 85,
    "diskUsage": 90,
    "responseTime": 5000
  },
  "apiConfig": {
    "port": 3000,
    "enableWebSocket": true,
    "sessionTimeout": 3600000,
    "maxConcurrentOperations": 5
  }
}
EOF
    
    # Create environment file template
    cat > "$CONFIG_DIR/.env.template" << EOF
# API Configuration
JWT_SECRET=your-jwt-secret-here
VALID_API_KEYS=your-api-key-1,your-api-key-2

# Server Configuration
SERVER_IP=$DEFAULT_SERVER_IP
SERVER_USER=$DEFAULT_SERVER_USER

# Database Configuration (if needed)
DB_PASSWORD=your-database-password

# Optional: Notification settings
SLACK_WEBHOOK_URL=
EMAIL_NOTIFICATIONS=false
EOF
    
    print_success "Configuration files created"
    print_warning "Please edit $CONFIG_DIR/.env.template and rename it to .env"
}

# Function to setup SSH keys
setup_ssh_keys() {
    print_status "Setting up SSH key authentication..."
    
    # Run the SSH setup
    cd "$INSTALL_DIR"
    node ssh-auth-manager.js
    
    if [ $? -eq 0 ]; then
        print_success "SSH key authentication configured"
    else
        print_warning "SSH key setup encountered issues. You may need to configure manually."
    fi
}

# Function to run initial diagnostics
run_initial_diagnostics() {
    print_status "Running initial server diagnostics..."
    
    cd "$INSTALL_DIR"
    node remote-server-manager.js diagnostics
    
    if [ $? -eq 0 ]; then
        print_success "Initial diagnostics completed successfully"
    else
        print_warning "Initial diagnostics failed. Server may not be accessible yet."
    fi
}

# Function to create desktop shortcut (Linux)
create_desktop_shortcut() {
    if command_exists xdg-user-dir; then
        local desktop_dir=$(xdg-user-dir DESKTOP 2>/dev/null)
        if [ -d "$desktop_dir" ]; then
            print_status "Creating desktop shortcut..."
            
            cat > "$desktop_dir/Remote Server Manager.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Remote Server Manager
Comment=Secure Remote Server Management Tool
Exec=$INSTALL_DIR/remote-server-manager.js start
Icon=utilities-terminal
Terminal=true
Categories=System;Network;
EOF
            
            chmod +x "$desktop_dir/Remote Server Manager.desktop"
            print_success "Desktop shortcut created"
        fi
    fi
}

# Function to display completion message
display_completion() {
    echo ""
    print_success "🎉 $TOOL_NAME installation completed successfully!"
    echo ""
    echo -e "${BLUE}📋 Quick Start Guide:${NC}"
    echo "  1. Configure your settings: edit $CONFIG_DIR/server-config.json"
    echo "  2. Set up environment: cp $CONFIG_DIR/.env.template $CONFIG_DIR/.env && edit .env"
    echo "  3. Start CLI mode: remote-server-manager start"
    echo "  4. Start API mode: remote-server-manager start --mode api"
    echo "  5. Run diagnostics: remote-server-manager diagnostics"
    echo "  6. Emergency mode: remote-server-manager emergency"
    echo ""
    echo -e "${BLUE}📁 Important Directories:${NC}"
    echo "  Installation: $INSTALL_DIR"
    echo "  Configuration: $CONFIG_DIR"
    echo "  Logs: $LOG_DIR"
    echo "  Backups: $BACKUP_DIR"
    echo ""
    echo -e "${BLUE}🔗 Useful Commands:${NC}"
    echo "  rsm start           # Start in CLI mode"
    echo "  rsm start --mode api # Start API server"
    echo "  rsm diagnostics     # Run server diagnostics"
    echo "  rsm emergency       # Emergency recovery mode"
    echo ""
    echo -e "${YELLOW}⚠️  Next Steps:${NC}"
    echo "  1. Edit the configuration file to match your server settings"
    echo "  2. Configure your API keys and JWT secret in the .env file"
    echo "  3. Test the SSH connection to your server"
    echo "  4. Run initial diagnostics to verify everything works"
    echo ""
    echo -e "${GREEN}✅ Installation complete! Happy server managing! 🚀${NC}"
}

# Main installation function
main() {
    echo ""
    echo -e "${BLUE}🔒 $TOOL_NAME - Installation Script${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo ""
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. Installing for root user."
    fi
    
    # Run installation steps
    check_requirements
    create_directories
    install_tool
    create_symlinks
    configure_tool
    
    # Optional steps
    echo ""
    read -p "Do you want to set up SSH key authentication now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_ssh_keys
    fi
    
    echo ""
    read -p "Do you want to run initial diagnostics? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_initial_diagnostics
    fi
    
    # Create desktop shortcut on Linux
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        create_desktop_shortcut
    fi
    
    display_completion
}

# Handle script interruption
trap 'print_error "Installation interrupted"; exit 1' INT TERM

# Run main function
main "$@"
