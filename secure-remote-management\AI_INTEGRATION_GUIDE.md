# 🤖 AI Assistant Integration Guide

This guide provides detailed instructions for integrating AI coding assistants (like Augment Code, Composer Agent, GitHub Copilot, etc.) with the Secure Remote Server Management Tool.

## 🎯 Overview

The tool provides a secure, controlled interface for AI assistants to manage your backend VPS server (45.93.8.197) through:
- RESTful API endpoints
- WebSocket real-time communication
- Structured command execution
- Comprehensive audit logging
- Emergency recovery procedures

## 🚀 Quick Setup for AI Assistants

### 1. Installation and Configuration

```bash
# Install the tool
git clone <repository-url>
cd secure-remote-management
./install.sh

# Configure for your server
nano ~/.config/remote-server-manager/server-config.json

# Set up API keys
cp ~/.config/remote-server-manager/.env.template ~/.config/remote-server-manager/.env
nano ~/.config/remote-server-manager/.env
```

### 2. Start API Server

```bash
# Start the API server for AI assistant access
remote-server-manager start --mode api

# Server will be available at http://localhost:3000
# WebSocket available for real-time updates
```

### 3. Test Connection

```bash
# Test basic functionality
curl -X GET http://localhost:3000/health

# Should return:
# {"status":"healthy","timestamp":"...","version":"1.0.0"}
```

## 🔌 API Integration Examples

### For Augment Code

```javascript
// Augment Code integration example
class AugmentServerManager {
  constructor(apiUrl = 'http://localhost:3000', apiKey) {
    this.apiUrl = apiUrl;
    this.apiKey = apiKey;
    this.token = null;
  }

  async authenticate() {
    const response = await fetch(`${this.apiUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        apiKey: this.apiKey,
        clientId: 'augment-code-assistant'
      })
    });
    
    const data = await response.json();
    if (data.success) {
      this.token = data.token;
      return true;
    }
    throw new Error('Authentication failed');
  }

  async executeCommand(command, options = {}) {
    if (!this.token) await this.authenticate();
    
    const response = await fetch(`${this.apiUrl}/server/command`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body: JSON.stringify({ command, options })
    });
    
    return await response.json();
  }

  async getDiagnostics() {
    if (!this.token) await this.authenticate();
    
    const response = await fetch(`${this.apiUrl}/server/diagnostics`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    return await response.json();
  }

  async restartService(service) {
    if (!this.token) await this.authenticate();
    
    const response = await fetch(`${this.apiUrl}/services/${service}/restart`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    return await response.json();
  }
}

// Usage example
const serverManager = new AugmentServerManager('http://localhost:3000', 'your-api-key');

// Run diagnostics
const diagnostics = await serverManager.getDiagnostics();
console.log('Server health score:', diagnostics.healthScore);

// Execute safe command
const result = await serverManager.executeCommand('systemctl status nginx');
console.log('Nginx status:', result.stdout);

// Restart service if needed
if (result.stdout.includes('failed')) {
  await serverManager.restartService('nginx');
}
```

### For Python-based AI Assistants

```python
import requests
import json
from typing import Dict, Any, Optional

class ServerManager:
    def __init__(self, api_url: str = "http://localhost:3000", api_key: str = None):
        self.api_url = api_url
        self.api_key = api_key
        self.token = None
        self.session = requests.Session()
    
    def authenticate(self) -> bool:
        """Authenticate with the server management API"""
        response = self.session.post(f"{self.api_url}/auth/login", json={
            "apiKey": self.api_key,
            "clientId": "python-ai-assistant"
        })
        
        if response.status_code == 200:
            data = response.json()
            self.token = data["token"]
            self.session.headers.update({"Authorization": f"Bearer {self.token}"})
            return True
        return False
    
    def execute_command(self, command: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a command on the remote server"""
        if not self.token:
            self.authenticate()
        
        response = self.session.post(f"{self.api_url}/server/command", json={
            "command": command,
            "options": options or {}
        })
        
        return response.json()
    
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get comprehensive server diagnostics"""
        if not self.token:
            self.authenticate()
        
        response = self.session.get(f"{self.api_url}/server/diagnostics")
        return response.json()
    
    def restart_service(self, service: str) -> Dict[str, Any]:
        """Restart a specific service"""
        if not self.token:
            self.authenticate()
        
        response = self.session.post(f"{self.api_url}/services/{service}/restart")
        return response.json()

# Usage example
server = ServerManager(api_key="your-api-key")

# Check server health
diagnostics = server.get_diagnostics()
print(f"Server health: {diagnostics['healthScore']}%")

# Check if services are running
if diagnostics['checks']['services']['nginx']['status'] != 'running':
    print("Nginx is down, restarting...")
    server.restart_service('nginx')
```

## 🛠️ Common AI Assistant Tasks

### 1. Server Health Monitoring

```javascript
// Check overall server health
async function checkServerHealth() {
  const diagnostics = await serverManager.getDiagnostics();
  
  if (diagnostics.healthScore < 70) {
    console.log('⚠️ Server health is degraded');
    
    // Check specific issues
    if (diagnostics.checks.resources?.cpu?.status === 'warning') {
      console.log('🔥 High CPU usage detected');
    }
    
    if (diagnostics.checks.services?.nginx?.status !== 'running') {
      console.log('🔧 Nginx service is down, attempting restart');
      await serverManager.restartService('nginx');
    }
  }
  
  return diagnostics;
}
```

### 2. Automated Service Recovery

```javascript
// Automated service recovery
async function recoverServices() {
  const services = ['nginx', 'mysql'];
  const results = [];
  
  for (const service of services) {
    try {
      const status = await serverManager.executeCommand(`systemctl status ${service}`);
      
      if (!status.stdout.includes('active (running)')) {
        console.log(`🔄 Restarting ${service}...`);
        const restart = await serverManager.restartService(service);
        results.push({ service, action: 'restarted', success: restart.success });
      } else {
        results.push({ service, action: 'none', success: true });
      }
    } catch (error) {
      results.push({ service, action: 'failed', error: error.message });
    }
  }
  
  return results;
}
```

### 3. Configuration Management

```javascript
// Safe configuration updates
async function updateNginxConfig(newConfig) {
  try {
    // Create backup first
    const backup = await serverManager.executeCommand(
      'cp /etc/nginx/fastpanel2-sites/streamdb_onl_usr.conf /tmp/nginx_backup_' + Date.now()
    );
    
    if (!backup.success) {
      throw new Error('Failed to create backup');
    }
    
    // Test configuration syntax
    const test = await serverManager.executeCommand('nginx -t');
    
    if (test.success) {
      // Reload nginx
      await serverManager.executeCommand('systemctl reload nginx');
      console.log('✅ Nginx configuration updated successfully');
    } else {
      throw new Error('Configuration test failed');
    }
    
  } catch (error) {
    console.error('❌ Configuration update failed:', error.message);
    // Restore backup if needed
  }
}
```

### 4. Database Operations

```javascript
// Safe database operations
async function checkDatabaseHealth() {
  try {
    // Check MySQL service
    const mysqlStatus = await serverManager.executeCommand('systemctl status mysql');
    
    if (!mysqlStatus.stdout.includes('active (running)')) {
      console.log('🔄 MySQL is down, restarting...');
      await serverManager.restartService('mysql');
    }
    
    // Test database connection
    const dbTest = await serverManager.executeCommand(
      'mysql --socket=/var/run/mysqld/mysqld.sock -u dbadmin_streamdb -e "SELECT 1;"'
    );
    
    if (dbTest.success) {
      console.log('✅ Database connection healthy');
    } else {
      console.log('❌ Database connection failed');
    }
    
  } catch (error) {
    console.error('Database health check failed:', error.message);
  }
}
```

## 🔒 Security Best Practices

### 1. API Key Management

```javascript
// Secure API key handling
class SecureServerManager {
  constructor() {
    // Never hardcode API keys
    this.apiKey = process.env.SERVER_MANAGER_API_KEY;
    if (!this.apiKey) {
      throw new Error('API key not found in environment variables');
    }
  }
  
  // Implement token refresh
  async refreshToken() {
    const response = await fetch(`${this.apiUrl}/auth/refresh`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    if (response.ok) {
      const data = await response.json();
      this.token = data.token;
    }
  }
}
```

### 2. Command Validation

```javascript
// Always validate commands before execution
function validateCommand(command) {
  const dangerousPatterns = [
    /rm\s+-rf/,
    /shutdown/,
    /reboot/,
    /passwd/,
    /userdel/
  ];
  
  for (const pattern of dangerousPatterns) {
    if (pattern.test(command)) {
      throw new Error(`Dangerous command detected: ${command}`);
    }
  }
  
  return true;
}

// Use dry-run for testing
async function safeExecute(command) {
  validateCommand(command);
  
  // Test with dry-run first
  const dryRun = await serverManager.executeCommand(command, { dryRun: true });
  
  if (dryRun.success) {
    // Execute for real
    return await serverManager.executeCommand(command);
  } else {
    throw new Error('Dry-run failed');
  }
}
```

### 3. Error Handling and Logging

```javascript
// Comprehensive error handling
async function executeWithLogging(command, context = {}) {
  const startTime = Date.now();
  
  try {
    console.log(`🔄 Executing: ${command}`);
    const result = await serverManager.executeCommand(command);
    
    const duration = Date.now() - startTime;
    console.log(`✅ Command completed in ${duration}ms`);
    
    // Log successful operations
    await logOperation({
      command,
      success: true,
      duration,
      context,
      timestamp: new Date().toISOString()
    });
    
    return result;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ Command failed after ${duration}ms: ${error.message}`);
    
    // Log failed operations
    await logOperation({
      command,
      success: false,
      error: error.message,
      duration,
      context,
      timestamp: new Date().toISOString()
    });
    
    throw error;
  }
}
```

## 🚨 Emergency Procedures

### 1. Emergency Service Restart

```javascript
// Emergency service restart procedure
async function emergencyRestart() {
  console.log('🚨 Initiating emergency service restart');
  
  const services = ['nginx', 'mysql'];
  const results = [];
  
  for (const service of services) {
    try {
      console.log(`🔄 Emergency restart: ${service}`);
      const result = await serverManager.restartService(service);
      results.push({ service, success: result.success });
    } catch (error) {
      console.error(`❌ Failed to restart ${service}: ${error.message}`);
      results.push({ service, success: false, error: error.message });
    }
  }
  
  // Restart PM2 processes
  try {
    await serverManager.executeCommand('pm2 restart all');
    results.push({ service: 'pm2', success: true });
  } catch (error) {
    results.push({ service: 'pm2', success: false, error: error.message });
  }
  
  return results;
}
```

### 2. System Recovery

```javascript
// Comprehensive system recovery
async function systemRecovery() {
  console.log('🔧 Starting system recovery procedure');
  
  // 1. Check system status
  const diagnostics = await serverManager.getDiagnostics();
  console.log(`Current health score: ${diagnostics.healthScore}%`);
  
  // 2. Restart failed services
  if (diagnostics.healthScore < 50) {
    await emergencyRestart();
  }
  
  // 3. Check disk space
  const diskCheck = await serverManager.executeCommand('df -h /');
  if (diskCheck.stdout.includes('100%')) {
    console.log('⚠️ Disk space critical - manual intervention required');
  }
  
  // 4. Verify recovery
  const postRecovery = await serverManager.getDiagnostics();
  console.log(`Post-recovery health score: ${postRecovery.healthScore}%`);
  
  return postRecovery.healthScore > diagnostics.healthScore;
}
```

## 📊 Monitoring Integration

### Real-time Monitoring with WebSocket

```javascript
// WebSocket integration for real-time monitoring
const io = require('socket.io-client');

class RealTimeMonitor {
  constructor(apiUrl, token) {
    this.socket = io(apiUrl);
    this.token = token;
    this.setupEventHandlers();
  }
  
  setupEventHandlers() {
    this.socket.on('connect', () => {
      console.log('🔌 Connected to monitoring server');
      this.socket.emit('authenticate', this.token);
    });
    
    this.socket.on('authenticated', (data) => {
      if (data.success) {
        this.socket.emit('subscribe-monitoring');
        console.log('✅ Authenticated and subscribed to monitoring');
      }
    });
    
    this.socket.on('command-executed', (data) => {
      console.log('📝 Command executed:', data);
    });
    
    this.socket.on('alert', (alert) => {
      console.log('🚨 Alert:', alert);
      this.handleAlert(alert);
    });
  }
  
  async handleAlert(alert) {
    if (alert.type === 'service-down') {
      console.log(`🔄 Auto-restarting service: ${alert.service}`);
      await serverManager.restartService(alert.service);
    }
  }
}
```

## 🔍 Troubleshooting

### Common Issues and Solutions

1. **Authentication Failed**
   ```javascript
   // Check API key and regenerate if needed
   if (!await serverManager.authenticate()) {
     console.log('❌ Authentication failed - check API key');
   }
   ```

2. **Command Execution Timeout**
   ```javascript
   // Use longer timeout for slow operations
   const result = await serverManager.executeCommand(command, { 
     timeout: 60000 // 60 seconds
   });
   ```

3. **Service Restart Failed**
   ```javascript
   // Check service status before restart
   const status = await serverManager.executeCommand('systemctl status nginx');
   if (status.stdout.includes('failed')) {
     // Service is in failed state, may need manual intervention
   }
   ```

This integration guide provides AI assistants with comprehensive tools for secure, automated server management while maintaining strict security controls and audit trails.
