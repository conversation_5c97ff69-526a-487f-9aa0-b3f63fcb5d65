#!/usr/bin/env node

/**
 * 🛡️ Secure Command Execution Framework
 * Safe command execution with whitelisting, sandboxing, and audit logging
 * 
 * Features:
 * - Command whitelisting and validation
 * - Sandboxed execution environment
 * - Comprehensive audit logging
 * - Automatic rollback capabilities
 * - Resource usage monitoring
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn, execSync } = require('child_process');
const crypto = require('crypto');
const SSHAuthManager = require('./ssh-auth-manager');

class SecureCommandExecutor {
  constructor(config = {}) {
    this.config = {
      serverIP: config.serverIP || '***********',
      serverUser: config.serverUser || 'root',
      maxExecutionTime: config.maxExecutionTime || 300000, // 5 minutes
      maxOutputSize: config.maxOutputSize || 10 * 1024 * 1024, // 10MB
      auditLogPath: config.auditLogPath || './logs/command-audit.log',
      backupDir: config.backupDir || './backups',
      dryRun: config.dryRun || false,
      ...config
    };

    this.sshManager = new SSHAuthManager(config);
    this.commandWhitelist = this.initializeCommandWhitelist();
    this.activeOperations = new Map();
  }

  /**
   * Initialize command whitelist with safe patterns
   */
  initializeCommandWhitelist() {
    return {
      // System diagnostics (read-only)
      diagnostics: [
        /^systemctl status \w+$/,
        /^ps aux(\s+\|\s+grep\s+\w+)?$/,
        /^netstat -[a-z]+$/,
        /^ss -[a-z]+$/,
        /^df -h$/,
        /^free -h$/,
        /^uptime$/,
        /^whoami$/,
        /^pwd$/,
        /^ls -[a-z]+ \/[\w\/\-\.]+$/,
        /^cat \/[\w\/\-\.]+\.log$/,
        /^tail -[0-9]+ \/[\w\/\-\.]+\.log$/,
        /^head -[0-9]+ \/[\w\/\-\.]+\.log$/,
        /^grep -[a-z]+ "[\w\s\-\.]+" \/[\w\/\-\.]+$/
      ],

      // Service management
      services: [
        /^systemctl (start|stop|restart|reload) (nginx|mysql|apache2)$/,
        /^pm2 (start|stop|restart|reload|status|list)(\s+[\w\-]+)?$/,
        /^pm2 logs(\s+[\w\-]+)?(\s+--lines\s+[0-9]+)?$/,
        /^service (nginx|mysql|apache2) (start|stop|restart|reload|status)$/
      ],

      // Configuration management (with backup)
      configuration: [
        /^cp \/[\w\/\-\.]+\.conf \/[\w\/\-\.]+\.conf\.backup\.[0-9]+$/,
        /^nginx -t$/,
        /^apache2ctl configtest$/,
        /^mysql --version$/,
        /^node --version$/,
        /^npm --version$/
      ],

      // File operations (restricted paths)
      files: [
        /^mkdir -p \/[\w\/\-\.]+$/,
        /^chmod [0-7]{3} \/[\w\/\-\.]+$/,
        /^chown [\w\-]+:[\w\-]+ \/[\w\/\-\.]+$/,
        /^ln -s \/[\w\/\-\.]+ \/[\w\/\-\.]+$/
      ],

      // Database operations (localhost socket only)
      database: [
        /^mysql --socket=\/var\/run\/mysqld\/mysqld\.sock -u [\w\-]+ -p[\w\-]+ -e "[\w\s\-\.,;'\"=\(\)]+;"$/,
        /^mysqldump --socket=\/var\/run\/mysqld\/mysqld\.sock -u [\w\-]+ -p[\w\-]+ [\w\-]+ > \/[\w\/\-\.]+\.sql$/,
        /^mysql --socket=\/var\/run\/mysqld\/mysqld\.sock -u [\w\-]+ -p[\w\-]+ [\w\-]+ < \/[\w\/\-\.]+\.sql$/,
        /^mysql --socket=\/var\/run\/mysqld\/mysqld\.sock -u [\w\-]+ -p[\w\-]+ -e "SHOW DATABASES;"$/,
        /^mysql --socket=\/var\/run\/mysqld\/mysqld\.sock -u [\w\-]+ -p[\w\-]+ -e "SHOW TABLES;" [\w\-]+$/
      ],

      // Network diagnostics
      network: [
        /^ping -c [1-5] [\w\.\-]+$/,
        /^curl -I https?:\/\/[\w\.\-\/]+$/,
        /^wget --spider https?:\/\/[\w\.\-\/]+$/,
        /^nslookup [\w\.\-]+$/,
        /^dig [\w\.\-]+$/
      ]
    };
  }

  /**
   * Validate command against whitelist
   */
  validateCommand(command) {
    const trimmedCommand = command.trim();
    
    // Check for dangerous patterns first
    const dangerousPatterns = [
      /rm\s+-rf/,
      /dd\s+if=/,
      /mkfs/,
      /fdisk/,
      /format/,
      /shutdown/,
      /reboot/,
      /halt/,
      /init\s+0/,
      /init\s+6/,
      /kill\s+-9/,
      /killall/,
      /pkill/,
      />\s*\/dev\/sd[a-z]/,
      />\s*\/dev\/hd[a-z]/,
      />\s*\/dev\/nvme/,
      /chmod\s+777\s+\//,
      /chown\s+.*\s+\//,
      /passwd/,
      /userdel/,
      /usermod/,
      /groupdel/,
      /crontab\s+-r/,
      /history\s+-c/,
      />\s*\/etc\//,
      />\s*\/boot\//,
      />\s*\/sys\//,
      />\s*\/proc\//
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(trimmedCommand)) {
        return {
          valid: false,
          reason: 'Command contains dangerous patterns',
          pattern: pattern.toString()
        };
      }
    }

    // Check against whitelist
    for (const [category, patterns] of Object.entries(this.commandWhitelist)) {
      for (const pattern of patterns) {
        if (pattern.test(trimmedCommand)) {
          return {
            valid: true,
            category,
            pattern: pattern.toString()
          };
        }
      }
    }

    return {
      valid: false,
      reason: 'Command not in whitelist',
      command: trimmedCommand
    };
  }

  /**
   * Execute command securely with full audit trail
   */
  async executeCommand(command, options = {}) {
    const operationId = crypto.randomUUID();
    const startTime = Date.now();
    
    try {
      // Validate command
      const validation = this.validateCommand(command);
      if (!validation.valid) {
        throw new Error(`Command validation failed: ${validation.reason}`);
      }

      // Log command execution start
      await this.auditLog('COMMAND_START', {
        operationId,
        command,
        validation,
        options,
        timestamp: new Date().toISOString()
      });

      // Check if dry run mode
      if (this.config.dryRun || options.dryRun) {
        await this.auditLog('DRY_RUN', {
          operationId,
          command,
          message: 'Command would be executed (dry run mode)'
        });
        
        return {
          success: true,
          operationId,
          stdout: '[DRY RUN] Command would be executed',
          stderr: '',
          executionTime: 0,
          dryRun: true
        };
      }

      // Create backup if needed
      const backupId = await this.createPreExecutionBackup(command, operationId);

      // Execute command with monitoring
      const result = await this.executeWithMonitoring(command, operationId, options);

      // Log successful execution
      await this.auditLog('COMMAND_SUCCESS', {
        operationId,
        command,
        result,
        executionTime: Date.now() - startTime,
        backupId
      });

      return {
        ...result,
        operationId,
        backupId,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      // Log failed execution
      await this.auditLog('COMMAND_FAILED', {
        operationId,
        command,
        error: error.message,
        executionTime: Date.now() - startTime
      });

      throw error;
    } finally {
      // Clean up active operation
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Execute command with resource monitoring
   */
  async executeWithMonitoring(command, operationId, options = {}) {
    return new Promise((resolve, reject) => {
      const sshCommand = `ssh ${this.config.serverUser}@${this.config.serverIP} "${command}"`;
      
      const child = spawn('ssh', [
        `${this.config.serverUser}@${this.config.serverIP}`,
        command
      ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        timeout: options.timeout || this.config.maxExecutionTime
      });

      // Track active operation
      this.activeOperations.set(operationId, {
        process: child,
        command,
        startTime: Date.now()
      });

      let stdout = '';
      let stderr = '';
      let outputSize = 0;

      child.stdout.on('data', (data) => {
        const chunk = data.toString();
        outputSize += chunk.length;
        
        if (outputSize > this.config.maxOutputSize) {
          child.kill('SIGTERM');
          reject(new Error('Output size limit exceeded'));
          return;
        }
        
        stdout += chunk;
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({
            success: true,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode: code
          });
        } else {
          reject(new Error(`Command failed with exit code ${code}: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(new Error(`Execution error: ${error.message}`));
      });

      // Set timeout
      setTimeout(() => {
        if (!child.killed) {
          child.kill('SIGTERM');
          reject(new Error('Command execution timeout'));
        }
      }, options.timeout || this.config.maxExecutionTime);
    });
  }

  /**
   * Create backup before potentially destructive operations
   */
  async createPreExecutionBackup(command, operationId) {
    // Identify files that might be modified
    const configFiles = this.extractConfigFiles(command);
    
    if (configFiles.length === 0) {
      return null;
    }

    const backupId = `backup_${operationId}_${Date.now()}`;
    const backupPath = path.join(this.config.backupDir, backupId);

    try {
      await fs.mkdir(backupPath, { recursive: true });

      for (const file of configFiles) {
        const backupCommand = `scp ${this.config.serverUser}@${this.config.serverIP}:${file} ${backupPath}/`;
        execSync(backupCommand);
      }

      await this.auditLog('BACKUP_CREATED', {
        operationId,
        backupId,
        files: configFiles,
        backupPath
      });

      return backupId;
    } catch (error) {
      await this.auditLog('BACKUP_FAILED', {
        operationId,
        error: error.message,
        files: configFiles
      });
      
      // Don't fail the operation for backup failure, but warn
      console.warn(`⚠️  Backup creation failed: ${error.message}`);
      return null;
    }
  }

  /**
   * Extract configuration files from command
   */
  extractConfigFiles(command) {
    const configPatterns = [
      /\/etc\/nginx\/[^\s]+\.conf/g,
      /\/etc\/apache2\/[^\s]+\.conf/g,
      /\/etc\/mysql\/[^\s]+\.cnf/g,
      /\/var\/www\/[^\s]+\/\.env/g,
      /\/home\/[^\s]+\/\.ssh\/[^\s]+/g
    ];

    const files = [];
    for (const pattern of configPatterns) {
      const matches = command.match(pattern);
      if (matches) {
        files.push(...matches);
      }
    }

    return [...new Set(files)]; // Remove duplicates
  }

  /**
   * Rollback operation using backup
   */
  async rollbackOperation(operationId, backupId) {
    try {
      if (!backupId) {
        throw new Error('No backup available for rollback');
      }

      const backupPath = path.join(this.config.backupDir, backupId);
      
      // Verify backup exists
      await fs.access(backupPath);

      // Get list of files in backup
      const backupFiles = await fs.readdir(backupPath);

      for (const file of backupFiles) {
        const localPath = path.join(backupPath, file);
        const remotePath = file; // Assuming file names match remote paths
        
        const restoreCommand = `scp ${localPath} ${this.config.serverUser}@${this.config.serverIP}:${remotePath}`;
        execSync(restoreCommand);
      }

      await this.auditLog('ROLLBACK_SUCCESS', {
        operationId,
        backupId,
        restoredFiles: backupFiles
      });

      return true;
    } catch (error) {
      await this.auditLog('ROLLBACK_FAILED', {
        operationId,
        backupId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Kill active operation
   */
  async killOperation(operationId) {
    const operation = this.activeOperations.get(operationId);
    
    if (!operation) {
      throw new Error('Operation not found or already completed');
    }

    try {
      operation.process.kill('SIGTERM');
      
      await this.auditLog('OPERATION_KILLED', {
        operationId,
        command: operation.command,
        duration: Date.now() - operation.startTime
      });

      return true;
    } catch (error) {
      await this.auditLog('KILL_FAILED', {
        operationId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get status of active operations
   */
  getActiveOperations() {
    const operations = [];
    
    for (const [operationId, operation] of this.activeOperations) {
      operations.push({
        operationId,
        command: operation.command,
        startTime: operation.startTime,
        duration: Date.now() - operation.startTime,
        status: 'running'
      });
    }

    return operations;
  }

  /**
   * Audit logging
   */
  async auditLog(event, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      ...data
    };

    try {
      // Ensure log directory exists
      const logDir = path.dirname(this.config.auditLogPath);
      await fs.mkdir(logDir, { recursive: true });

      // Append to audit log
      await fs.appendFile(
        this.config.auditLogPath,
        JSON.stringify(logEntry) + '\n'
      );
    } catch (error) {
      console.error('Failed to write audit log:', error.message);
    }
  }
}

module.exports = SecureCommandExecutor;

// CLI usage
if (require.main === module) {
  const executor = new SecureCommandExecutor();
  const command = process.argv[2];
  
  if (!command) {
    console.error('Usage: node command-executor.js "<command>"');
    process.exit(1);
  }

  executor.executeCommand(command)
    .then((result) => {
      console.log('✅ Command executed successfully:');
      console.log(result);
    })
    .catch((error) => {
      console.error('❌ Command execution failed:', error.message);
      process.exit(1);
    });
}
